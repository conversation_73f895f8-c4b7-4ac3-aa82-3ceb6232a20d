import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/bot_default_preferences.dart';

// User preferences model
class UserPreferences {
  final String id;
  final String userId;
  final String themeMode;
  final bool notificationsEnabled;
  final bool tradingNotifications;
  final bool priceAlerts;
  final String preferredCurrency;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserPreferences({
    required this.id,
    required this.userId,
    required this.themeMode,
    required this.notificationsEnabled,
    required this.tradingNotifications,
    required this.priceAlerts,
    required this.preferredCurrency,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      id: json['id'],
      userId: json['user_id'],
      themeMode: json['theme_mode'] ?? 'system',
      notificationsEnabled: json['notifications_enabled'] ?? true,
      tradingNotifications: json['trading_notifications'] ?? true,
      priceAlerts: json['price_alerts'] ?? true,
      preferredCurrency: json['preferred_currency'] ?? 'USD',
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'theme_mode': themeMode,
      'notifications_enabled': notificationsEnabled,
      'trading_notifications': tradingNotifications,
      'price_alerts': priceAlerts,
      'preferred_currency': preferredCurrency,
    };
  }

  UserPreferences copyWith({
    String? themeMode,
    bool? notificationsEnabled,
    bool? tradingNotifications,
    bool? priceAlerts,
    String? preferredCurrency,
  }) {
    return UserPreferences(
      id: id,
      userId: userId,
      themeMode: themeMode ?? this.themeMode,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      tradingNotifications: tradingNotifications ?? this.tradingNotifications,
      priceAlerts: priceAlerts ?? this.priceAlerts,
      preferredCurrency: preferredCurrency ?? this.preferredCurrency,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }
}

class UserPreferencesService {
  static final UserPreferencesService _instance = UserPreferencesService._();
  factory UserPreferencesService() => _instance;
  UserPreferencesService._();

  final SupabaseClient _supabase = Supabase.instance.client;

  // Get user preferences
  Future<UserPreferences?> getUserPreferences() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return null;

      final response = await _supabase
          .from('user_preferences')
          .select()
          .eq('user_id', user.id)
          .maybeSingle();

      if (response == null) {
        // Create default preferences
        return await createDefaultPreferences(user.id);
      }

      return UserPreferences.fromJson(response);
    } catch (e) {
      debugPrint('Error getting user preferences: $e');
      return null;
    }
  }

  // Create default preferences for new user
  Future<UserPreferences> createDefaultPreferences(String userId) async {
    try {
      final defaultPrefs = {
        'user_id': userId,
        'theme_mode': 'system',
        'notifications_enabled': true,
        'trading_notifications': true,
        'price_alerts': true,
        'preferred_currency': 'USD',
      };

      final response = await _supabase
          .from('user_preferences')
          .insert(defaultPrefs)
          .select()
          .single();

      return UserPreferences.fromJson(response);
    } catch (e) {
      debugPrint('Error creating default preferences: $e');
      rethrow;
    }
  }

  // Update user preferences
  Future<UserPreferences?> updateUserPreferences(
    UserPreferences preferences,
  ) async {
    try {
      final response = await _supabase
          .from('user_preferences')
          .update(preferences.toJson())
          .eq('user_id', preferences.userId)
          .select()
          .single();

      return UserPreferences.fromJson(response);
    } catch (e) {
      debugPrint('Error updating user preferences: $e');
      return null;
    }
  }

  // Update specific preference
  Future<bool> updatePreference(String key, dynamic value) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return false;

      await _supabase
          .from('user_preferences')
          .update({key: value, 'updated_at': DateTime.now().toIso8601String()})
          .eq('user_id', user.id);

      return true;
    } catch (e) {
      debugPrint('Error updating preference $key: $e');
      return false;
    }
  }

  // Delete user preferences (for account deletion)
  Future<bool> deleteUserPreferences(String userId) async {
    try {
      await _supabase.from('user_preferences').delete().eq('user_id', userId);

      return true;
    } catch (e) {
      debugPrint('Error deleting user preferences: $e');
      return false;
    }
  }

  /// Get user's bot default preferences
  Future<BotDefaultPreferences?> getBotDefaultPreferences() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        debugPrint('❌ No authenticated user found');
        return null;
      }

      debugPrint('📊 Fetching bot default preferences for user: ${user.id}');

      final response = await _supabase
          .from('user_preferences')
          .select('*')
          .eq('user_id', user.id)
          .maybeSingle();

      if (response == null) {
        debugPrint(
          '📊 No user preferences found, creating default preferences',
        );
        return await _createDefaultBotPreferences();
      }

      debugPrint('📊 Bot default preferences loaded successfully');
      return BotDefaultPreferences.fromJson(response);
    } catch (e) {
      debugPrint('❌ Error fetching bot default preferences: $e');
      return null;
    }
  }

  /// Update user's bot default preferences
  Future<bool> updateBotDefaultPreferences(
    BotDefaultPreferences preferences,
  ) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        debugPrint('❌ No authenticated user found');
        return false;
      }

      debugPrint('📊 Updating bot default preferences for user: ${user.id}');

      final data = preferences.toJson();
      data['user_id'] = user.id;
      data['updated_at'] = DateTime.now().toIso8601String();

      // Check if preferences exist
      final existing = await _supabase
          .from('user_preferences')
          .select('id')
          .eq('user_id', user.id)
          .maybeSingle();

      if (existing != null) {
        // Update existing preferences
        await _supabase
            .from('user_preferences')
            .update(data)
            .eq('user_id', user.id);
      } else {
        // Insert new preferences
        data['created_at'] = DateTime.now().toIso8601String();
        await _supabase.from('user_preferences').insert(data);
      }

      debugPrint('✅ Bot default preferences updated successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Error updating bot default preferences: $e');
      return false;
    }
  }

  /// Create default bot preferences for new user
  Future<BotDefaultPreferences> _createDefaultBotPreferences() async {
    final defaultPrefs = BotDefaultPreferences.createDefault();

    try {
      final user = _supabase.auth.currentUser;
      if (user != null) {
        final data = defaultPrefs.toJson();
        data['user_id'] = user.id;
        data['created_at'] = DateTime.now().toIso8601String();
        data['updated_at'] = DateTime.now().toIso8601String();

        await _supabase.from('user_preferences').insert(data);

        debugPrint('✅ Default bot preferences created for user');
      }
    } catch (e) {
      debugPrint('❌ Error creating default bot preferences: $e');
    }

    return defaultPrefs;
  }

  /// Reset bot preferences to default values
  Future<bool> resetBotPreferencesToDefaults() async {
    try {
      final defaultPrefs = BotDefaultPreferences.createDefault();
      return await updateBotDefaultPreferences(defaultPrefs);
    } catch (e) {
      debugPrint('❌ Error resetting bot preferences to defaults: $e');
      return false;
    }
  }
}
