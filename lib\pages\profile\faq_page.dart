import 'package:dextrip_app/utils/responsive_helper.dart';
import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../../theme/app_theme.dart';

class FAQPage extends StatelessWidget {
  const FAQPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
       backgroundColor: ResponsiveHelper.isDesktop(context) ? Colors.transparent : Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('FAQ'),
        elevation: 0,
        backgroundColor: Colors.transparent,
      ),
      body: ListView(
        padding: const EdgeInsets.all(20),
        children: [
          _buildFAQItem(
            'What is DexTrip?',
            'DexTrip is a comprehensive crypto trading platform that provides real-time market data, automated trading bots, AI-powered recommendations, and portfolio management tools.',
          ),
          _buildFAQItem(
            'How do I create a wallet?',
            'You can create a wallet by going to the Wallet tab and clicking on "Create New Wallet". We support both Solana wallets and demo paper wallets for testing.',
          ),
          _buildFAQItem(
            'Are my funds safe?',
            'Yes, we use industry-standard security measures including encryption and secure key management. Your private keys are never stored on our servers.',
          ),
          _buildFAQItem(
            'How do trading bots work?',
            'Our trading bots use advanced algorithms to automatically execute trades based on market conditions and your predefined strategies.',
          ),
          _buildFAQItem(
            'What are the fees?',
            'DexTrip charges competitive fees for trading and bot services. Please check our pricing page for detailed information.',
          ),
          _buildFAQItem(
            'How can I contact support?',
            'You can reach our support team through the Contact Us page or email <NAME_EMAIL>.',
          ),
        ],
      ),
    );
  }

  Widget _buildFAQItem(String question, String answer) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ExpansionTile(
        leading: Icon(LucideIcons.handHelping, color: AppTheme.primaryColor),
        title: Text(
          question,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              answer,
              style: TextStyle(
                color: Colors.grey[600],
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }
}