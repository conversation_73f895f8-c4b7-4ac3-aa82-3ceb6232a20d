Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter run -d chrome --web-port=3000

## exception

ExistingDartDevelopmentServiceException: DartDevelopmentServiceException: JSON-RPC error 100: Existing VM service clients prevent DDS from taking control.

```
```

## flutter doctor

```
[32m[✓][39m Flutter (Channel stable, 3.32.5, on macOS 15.5 24F74 darwin-arm64, locale en-IN) [1,371ms]
    [32m•[39m Flutter version 3.32.5 on channel stable at
      /Users/<USER>/development/flutter
    [32m•[39m Upstream repository https://github.com/flutter/flutter.git
    [32m•[39m Framework revision fcf2c11572 (6 weeks ago), 2025-06-24 11:44:07 -0700
    [32m•[39m Engine revision dd93de6fb1
    [32m•[39m Dart version 3.8.1
    [32m•[39m DevTools version 2.45.1

[33m[!][39m Android toolchain - develop for Android devices (Android SDK version 35.0.1) [1,386ms]
    [32m•[39m Android SDK at /Users/<USER>/Library/Android/sdk
    [31m✗[39m cmdline-tools component is missing.
      Try installing or updating Android Studio.
      Alternatively, download the tools from
      https://developer.android.com/studio#command-line-tools-only and make sure to set the
      ANDROID_HOME environment variable.
      See https://developer.android.com/studio/command-line for more details.
    [31m✗[39m Android license status unknown.
      Run `flutter doctor --android-licenses` to accept the SDK licenses.
      See https://flutter.dev/to/macos-android-setup for more details.

[32m[✓][39m Xcode - develop for iOS and macOS (Xcode 16.4) [2.0s]
    [32m•[39m Xcode at /Applications/Xcode.app/Contents/Developer
    [32m•[39m Build 16F6
    [32m•[39m CocoaPods version 1.16.2

[32m[✓][39m Chrome - develop for the web [4ms]
    [32m•[39m CHROME_EXECUTABLE = /Applications/Brave Browser.app/Contents/MacOS/Brave Browser

[32m[✓][39m Android Studio (version 2024.3) [4ms]
    [32m•[39m Android Studio at /Applications/Android Studio.app/Contents
    [32m•[39m Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    [32m•[39m Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    [32m•[39m Java version OpenJDK Runtime Environment (build 21.0.6+-13355223-b631.42)

[32m[✓][39m VS Code (version 1.102.3) [2ms]
    [32m•[39m VS Code at /Applications/Visual Studio Code.app/Contents
    [32m•[39m Flutter extension version 3.116.0

[32m[✓][39m Connected device (2 available) [6.1s]
    [32m•[39m iPhone 16 Plus (mobile) • FAFCEE60-58FE-4C83-8E8E-FADBCA620D33 • ios            •
      com.apple.CoreSimulator.SimRuntime.iOS-18-5 (simulator)
    [32m•[39m Chrome (web)            • chrome                               • web-javascript • Brave
      Browser ************
    [33m![39m Error: Browsing on the local area network for Office. Ensure the device is unlocked and
      associated with the same local area network as this Mac. (code -27)

[32m[✓][39m Network resources [429ms]
    [32m•[39m All expected network resources are available.

[33m![39m Doctor found issues in 1 category.
```
