# Trading Bot Database Issue - Debugging Guide

## 🔍 **How to Debug the Issue**

### Step 1: Run Database Schema Setup
1. Open your Supabase dashboard
2. Go to SQL Editor
3. Copy and paste the contents of `database_schema_setup.sql`
4. Run the script to ensure all columns and policies exist

### Step 2: Use the Database Test Widget
1. Open your Flutter app
2. Go to Profile page
3. Look for "Debug" section
4. Tap "Database Test"
5. Tap "Run Database Tests"
6. Check the output for any errors

### Step 3: Check Debug Logs
When you try to create a trading bot, check the Flutter console for detailed logs:

```
🚀 Starting trading bot creation process...
👤 Current user ID: [user-id]
🤖 Creating trading bot for user: [user-id]
📊 Bot details: [name], [symbol], [strategy]
💰 Investment: [amount], Stop Loss: [%], Take Profit: [%]
🎯 Start Immediately: [true/false], Risk: [%]
📈 Indicators count: [number]
📊 Indicators JSON: [json-data]
💾 Prepared bot data for insertion:
  user_id: [user-id] (String)
  name: [bot-name] (String)
  coin_id: [coin-id] (String)
  ... [all other fields]
🔗 Attempting database insertion...
✅ Database connection test successful
✅ Trading bot created successfully!
```

### Step 4: Common Issues and Solutions

#### Issue 1: User Not Authenticated
**Symptoms:** `❌ User not authenticated - userId is null`
**Solution:** 
- Make sure you're signed in to the app
- Check if Supabase auth is working in the debug test

#### Issue 2: Database Connection Failed
**Symptoms:** `❌ Database connection test failed`
**Solution:**
- Check your Supabase URL and API keys in `lib/config/environment.dart`
- Verify your internet connection
- Check Supabase dashboard for any service issues

#### Issue 3: Missing Database Columns
**Symptoms:** `❌ Schema test failed - missing columns`
**Solution:**
- Run the `database_schema_setup.sql` script in Supabase
- Check if all required columns exist in the `trading_bots` table

#### Issue 4: Permission Denied
**Symptoms:** `🔒 Permission error - check RLS policies`
**Solution:**
- Run the RLS policy setup from `database_schema_setup.sql`
- Make sure the user is authenticated
- Check if the policies allow the current user to insert data

#### Issue 5: Data Type Mismatch
**Symptoms:** `📊 Column error - check database schema`
**Solution:**
- Verify that all data types match between the app and database
- Check if DECIMAL fields are properly formatted
- Ensure JSONB fields are valid JSON

### Step 5: Manual Database Verification

After attempting to create a trading bot, check your Supabase database:

1. Go to Supabase Dashboard → Table Editor
2. Open the `trading_bots` table
3. Check if a new record was created
4. Verify all fields are populated correctly

### Step 6: Test with Minimal Data

Try creating a trading bot with minimal data to isolate the issue:

```dart
// Minimal test data
final result = await TradingBotService().createTradingBot(
  name: 'Test Bot',
  coin: CoinData(id: 'test', symbol: 'TEST', name: 'Test'),
  strategy: 'DCA',
  investmentAmount: 100.0,
  stopLoss: 5.0,
  takeProfit: 10.0,
  indicators: [],
  startImmediately: false,
);
```

## 🛠️ **Quick Fixes**

### Fix 1: Reset Database Schema
```sql
-- Drop and recreate the table (WARNING: This will delete all data)
DROP TABLE IF EXISTS public.trading_bots CASCADE;
-- Then run the full schema setup script
```

### Fix 2: Check Required Environment Variables
Ensure these are set in `lib/config/environment.dart`:
- `supabaseUrl`
- `supabaseAnonKey`

### Fix 3: Verify Supabase Project Settings
1. Check if your Supabase project is active
2. Verify API keys are correct
3. Ensure RLS is properly configured

## 📋 **Expected Database Record**

When a trading bot is successfully created, you should see a record like this:

```json
{
  "id": "uuid-here",
  "user_id": "user-uuid-here",
  "name": "BTC Bot",
  "coin_id": "btc",
  "coin_symbol": "BTC",
  "coin_name": "Bitcoin",
  "coin_image": null,
  "strategy": "DCA",
  "investment_amount": 100.00,
  "stop_loss": 5.00,
  "take_profit": 10.00,
  "indicators": [{"type": "rsi", "parameters": {"period": 14}}],
  "is_active": false,
  "start_immediately": false,
  "risk_percentage": 2.00,
  "trading_24_7": true,
  "trading_start_time": null,
  "trading_end_time": null,
  "is_archived": false,
  "total_trades": 0,
  "win_rate": 0.00,
  "total_earned": 0.00,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

## 🚨 **If Nothing Works**

1. Check the Flutter console for any error messages
2. Use the Database Test Widget to isolate the issue
3. Verify your Supabase project is working with a simple query
4. Try creating a record manually in Supabase to test the schema
5. Check if there are any network connectivity issues
