import 'dart:async';
import 'package:dextrip_app/utils/responsive_helper.dart';
import 'package:dextrip_app/widgets/coin_filter_modal.dart';
import 'package:dextrip_app/widgets/custom_dialog.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../models/coin_model.dart';
import '../services/dexscreener_service.dart';
import '../services/supabase_coin_service.dart';
import '../services/app_lifecycle_service.dart';

import '../utils/currency_formatter.dart';
import 'coin_detail_page.dart';

class CoinPage extends StatefulWidget {
  const CoinPage({
    super.key,
    required this.onThemeToggle,
    required this.isDarkMode,
    required this.isSignedIn,
    required this.onSignIn,
  });

  final VoidCallback onThemeToggle;
  final bool isDarkMode;
  final bool isSignedIn;
  final VoidCallback onSignIn;

  @override
  State<CoinPage> createState() => _CoinPageState();
}

class _CoinPageState extends State<CoinPage> with WidgetsBindingObserver {
  final TextEditingController _searchController = TextEditingController();
  final SupabaseCoinService _supabaseCoinService = SupabaseCoinService();
  final AppLifecycleService _lifecycleService = AppLifecycleService();
  final ScrollController _scrollController = ScrollController();

  List<CoinData> _coins = [];
  List<CoinData> _filteredCoins = [];
  bool _isLoading = true;
  String _error = '';
  Timer? _debounceTimer;
  StreamSubscription<List<CoinData>>? _coinStreamSubscription;
  StreamSubscription<AppPage>? _pageChangeSubscription;
  Map<String, dynamic> _filters = {};
  String _currentSearchQuery = '';
  bool _isSearchMode = false;
  bool _useSupabaseData = true;

  // Pagination variables (only for desktop)
  int _currentPage = 1;
  int _itemsPerPage = 25;
  int _totalItems = 0;
  List<CoinData> _visibleCoins = [];

  // Visibility tracking for real-time optimization
  final Map<String, GlobalKey> _coinKeys = {};
  final Set<String> _visibleCoinIds = {};
  Timer? _visibilityCheckTimer;
  bool _isPageVisible = false;
  Timer? _realTimeUpdateTimer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Check if coins page is currently active
    _isPageVisible = _lifecycleService.currentPage == AppPage.coins;

    _initializeDataSource();
    _searchController.addListener(_onSearchChanged);
    _setupLifecycleListener();
    _setupScrollListener();
    _startVisibilityTracking();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _searchController.dispose();
    _debounceTimer?.cancel();
    _coinStreamSubscription?.cancel();
    _pageChangeSubscription?.cancel();
    _visibilityCheckTimer?.cancel();
    _realTimeUpdateTimer?.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    _isPageVisible = state == AppLifecycleState.resumed;

    if (_isPageVisible) {
      debugPrint('🔄 App resumed - restarting real-time updates');
      _startOptimizedRealTimeUpdates();
    } else {
      debugPrint('⏸️ App paused - stopping real-time updates');
      _stopRealTimeUpdates();
    }
  }

  void _setupLifecycleListener() {
    _pageChangeSubscription = _lifecycleService.pageChangeStream.listen((page) {
      if (page == AppPage.coins) {
        _isPageVisible = true;
        debugPrint(
          '🔄 Coins page became active - starting optimized real-time updates',
        );
        _startOptimizedRealTimeUpdates();
      } else {
        _isPageVisible = false;
        debugPrint(
          '⏸️ Coins page became inactive - stopping real-time updates',
        );
        _stopRealTimeUpdates();
      }
    });
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      // Debounce scroll events to avoid excessive visibility checks
      _debounceTimer?.cancel();
      _debounceTimer = Timer(const Duration(milliseconds: 300), () {
        _checkVisibleCoins();
      });
    });
  }

  void _startVisibilityTracking() {
    _visibilityCheckTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      if (_isPageVisible && mounted) {
        _checkVisibleCoins();
      }
    });
  }

  void _checkVisibleCoins() {
    if (!mounted || _visibleCoins.isEmpty) return;

    final Set<String> newVisibleIds = {};
    final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final viewportHeight = renderBox.size.height;

    for (final coin in _visibleCoins) {
      final key = _coinKeys[coin.id];
      if (key?.currentContext != null) {
        final RenderBox? coinRenderBox =
            key!.currentContext!.findRenderObject() as RenderBox?;
        if (coinRenderBox != null) {
          final position = coinRenderBox.localToGlobal(
            Offset.zero,
            ancestor: renderBox,
          );
          final coinHeight = coinRenderBox.size.height;

          // Check if coin is visible in viewport (with some buffer)
          if (position.dy < viewportHeight + 100 &&
              position.dy + coinHeight > -100) {
            newVisibleIds.add(coin.id);
          }
        }
      }
    }

    // Update visible coins if changed
    if (newVisibleIds.length != _visibleCoinIds.length ||
        !newVisibleIds.every(_visibleCoinIds.contains)) {
      _visibleCoinIds.clear();
      _visibleCoinIds.addAll(newVisibleIds);

      debugPrint(
        '👁️ Visible coins updated: ${_visibleCoinIds.length} coins visible',
      );
      _updateRealTimeSubscription();
    }
  }

  void _startOptimizedRealTimeUpdates() {
    if (!_isPageVisible || !_useSupabaseData) return;

    debugPrint('🚀 Starting optimized real-time updates');
    _checkVisibleCoins();

    // Start real-time updates with shorter intervals for visible coins
    _realTimeUpdateTimer?.cancel();
    _realTimeUpdateTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (_isPageVisible && _visibleCoinIds.isNotEmpty) {
        _updateVisibleCoinsOnly();
      }
    });
  }

  void _stopRealTimeUpdates() {
    debugPrint('🛑 Stopping real-time updates');
    _realTimeUpdateTimer?.cancel();
    _supabaseCoinService.pauseRealTimeSubscription();
  }

  void _updateRealTimeSubscription() {
    if (!_isPageVisible || _visibleCoinIds.isEmpty) {
      debugPrint(
        '🚫 Skipping subscription update - page not visible or no visible coins',
      );
      return;
    }

    // Don't subscribe during search mode to save bandwidth
    if (_isSearchMode && _currentSearchQuery.isNotEmpty) {
      debugPrint(
        '🔍 Skipping subscription during search mode to save bandwidth',
      );
      return;
    }

    // Subscribe only to visible coins when not in search mode
    final visibleCoinsList = _visibleCoinIds.toList();
    debugPrint(
      '🎯 Updating subscription for ${visibleCoinsList.length} visible coins',
    );

    _supabaseCoinService.initializeRealTimeSubscription(
      visibleCoinIds: visibleCoinsList,
    );
  }

  Future<void> _updateVisibleCoinsOnly() async {
    if (_visibleCoinIds.isEmpty) return;

    try {
      // Get updates only for visible coins
      final updatedCoins = await _supabaseCoinService.getCoinsById(
        _visibleCoinIds.toList(),
      );

      if (updatedCoins.isNotEmpty && mounted) {
        _handleRealTimeUpdate(updatedCoins);
      }
    } catch (e) {
      debugPrint('❌ Error updating visible coins: $e');
    }
  }

  void _handleRealTimeUpdate(List<CoinData> newCoins) {
    final now = DateTime.now();
    final timeString =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}';
    debugPrint(
      '📱 [$timeString] Coin page received real-time update: ${newCoins.length} coins',
    );

    setState(() {
      if (_isSearchMode && _currentSearchQuery.isNotEmpty) {
        // During search mode, only update existing coins, don't replace the filtered list
        debugPrint(
          '🔍 Search mode: Updating existing coins without changing filter',
        );

        // Update existing coins in the main list
        for (final newCoin in newCoins) {
          final existingIndex = _coins.indexWhere((coin) {
            if (coin.tokenAddress != null && newCoin.tokenAddress != null) {
              return coin.tokenAddress!.toLowerCase() ==
                  newCoin.tokenAddress!.toLowerCase();
            }
            return coin.id == newCoin.id ||
                coin.symbol.toLowerCase() == newCoin.symbol.toLowerCase();
          });

          if (existingIndex != -1) {
            _coins[existingIndex] = newCoin;
          }
        }

        // Re-apply the current search filter to maintain search results
        _filteredCoins = _filterCoins(_coins, _currentSearchQuery);
        debugPrint(
          '🔍 Search mode: Maintained ${_filteredCoins.length} filtered results',
        );
      } else {
        // Normal mode: replace all coins and apply any current filter
        _coins = newCoins;
        _filteredCoins = _searchController.text.isEmpty
            ? newCoins
            : _filterCoins(newCoins, _searchController.text);
        debugPrint(
          '📱 [$timeString] Normal mode: Updated ${_coins.length} total coins, ${_filteredCoins.length} filtered',
        );
      }

      _totalItems = _filteredCoins.length;
      _updateVisibleCoins();
      _isLoading = false;
      _error = '';
    });
  }

  void _updateVisibleCoins() {
    // Only use pagination on desktop
    if (_isDesktop()) {
      final startIndex = (_currentPage - 1) * _itemsPerPage;
      final endIndex = (startIndex + _itemsPerPage).clamp(
        0,
        _filteredCoins.length,
      );
      _visibleCoins = _filteredCoins.sublist(startIndex, endIndex);
    } else {
      _visibleCoins = _filteredCoins;
    }
  }

  bool _isDesktop() {
    return MediaQuery.of(context).size.width >= 1024;
  }

  Future<void> _initializeDataSource() async {
    if (_useSupabaseData) {
      await _initializeSupabaseData();
    } else {
      await _loadTrendingCoins();
    }
  }

  Future<void> _initializeSupabaseData() async {
    try {
      // Only initialize real-time subscription if page is active
      if (_isPageVisible) {
        await _supabaseCoinService.initializeRealTimeSubscription();
      }

      debugPrint('📱 Setting up coin stream subscription...');
      _coinStreamSubscription = _supabaseCoinService.coinStream.listen(
        (coins) {
          // Only process updates if page is visible and mounted
          if (mounted && _isPageVisible) {
            final now = DateTime.now();
            final timeString =
                '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}';
            debugPrint(
              '📱 [$timeString] Coin stream received: ${coins.length} coins',
            );
            _handleRealTimeUpdate(coins);
          } else {
            debugPrint(
              '📱 Page not visible or widget not mounted, skipping update',
            );
          }
        },
        onError: (error) {
          if (mounted) {
            setState(() {
              _error = 'Real-time connection error: $error';
              _isLoading = false;
            });
          }
        },
      );

      await _loadSupabaseCoins();
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to initialize Supabase data: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadSupabaseCoins() async {
    setState(() {
      _isLoading = true;
      if (_currentPage == 1) {
        _coins.clear(); // Only clear on first page
      }
    });

    try {
      print('🔄 Loading Supabase coins (no limit) for page $_currentPage...');

      // Remove coin list limit - load all available coins dynamically
      final coins = await _supabaseCoinService.getCoins(
        limit: 10000, // Large limit to get all coins
        offset: 0, // Start from beginning
      );

      print('✅ Loaded ${coins.length} coins from Supabase (unlimited)');

      

      setState(() {
        _coins = coins;
        _filteredCoins = List.from(_coins);
        _totalItems = _coins.length;
        _updateVisibleCoins();
        _isLoading = false;
        _error = '';
      });

      // Start real-time updates for visible items only
      if (coins.isNotEmpty) {
        _startRealTimeUpdatesForVisibleItems();
      }
    } catch (e) {
      setState(() {
        _error = 'Failed to load coins: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadTrendingCoins() async {
    setState(() {
      _isLoading = true;
      _coins.clear();
    });

    try {
      final coins = await CoinGeckoService.getTrendingCoins();
      if (kDebugMode) {
        print(coins);
      }
      setState(() {
        _coins = coins;
        _filteredCoins = List.from(_coins);
        _totalItems = _filteredCoins.length;
        _currentPage = 1;
        _updateVisibleCoins();
        _isLoading = false;
        _error = '';
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _onSearchChanged() {
    final query = _searchController.text.trim();
    _debounceTimer?.cancel();

    if (query.isEmpty) {
      _searchCoins('');
      return;
    }

    if (query.length < 3) {
      setState(() {
        _filteredCoins = _filterCoins(_coins, query);
        _totalItems = _filteredCoins.length;
        _currentPage = 1;
        _updateVisibleCoins();
      });
      return;
    }

    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _searchCoins(query);
    });
  }

  List<CoinData> _filterCoins(List<CoinData> coins, String query) {
    final queryLower = query.toLowerCase();
    return coins.where((coin) {
      return coin.name.toLowerCase().contains(queryLower) ||
          coin.symbol.toLowerCase().contains(queryLower) ||
          coin.displayName.toLowerCase().contains(queryLower) ||
          coin.id.toLowerCase().contains(queryLower) ||
          (coin.tokenAddress?.toLowerCase().contains(queryLower) ?? false);
    }).toList();
  }

  /// Copy token address to clipboard
  void _copyTokenAddress(String tokenAddress, String tokenName) {
    Clipboard.setData(ClipboardData(text: tokenAddress));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$tokenName address copied to clipboard'),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _startRealTimeUpdatesForVisibleItems() {
    _coinStreamSubscription = _supabaseCoinService.coinStream.listen(
      (updatedCoins) {
        if (mounted && _isPageVisible && updatedCoins.isNotEmpty) {
          _updateVisibleCoinsRealTime(updatedCoins);
        } else {
          debugPrint('📱 Page not visible, skipping real-time update');
        }
      },
      onError: (error) {
        print('❌ Real-time update error: $error');
      },
    );

    // Only initialize if page is visible
    if (_isPageVisible) {
      _supabaseCoinService.initializeRealTimeSubscription();
    }
  }

  void _updateVisibleCoinsRealTime(List<CoinData> updatedCoins) {
    // Get currently visible items based on scroll position/pagination
    final startIndex = (_currentPage - 1) * _itemsPerPage;
    final endIndex = (startIndex + _itemsPerPage).clamp(0, _coins.length);

    final now = DateTime.now();
    final timeString =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}';

    print(
      '🔄 Real-time update for visible items: ${startIndex}-${endIndex} (Page $_currentPage) at $timeString',
    );

    bool hasUpdates = false;

    // Update only coins in current viewport
    for (int i = startIndex; i < endIndex; i++) {
      if (i < _coins.length) {
        final currentCoin = _coins[i];
        final updatedCoin = updatedCoins.firstWhere(
          (coin) => coin.id == currentCoin.id,
          orElse: () => currentCoin,
        );

        // Check if price, market cap, or volume changed
        if (updatedCoin.currentPrice != currentCoin.currentPrice ||
            updatedCoin.marketCap != currentCoin.marketCap ||
            updatedCoin.totalVolume != currentCoin.totalVolume) {
          // Format values for logging
          final price = updatedCoin.currentPrice != null
              ? '\$${_formatPrice(updatedCoin.currentPrice!)}'
              : 'N/A';
          final marketCap = updatedCoin.marketCap != null
              ? _formatCurrency(updatedCoin.marketCap!)
              : 'N/A';
          final volume = updatedCoin.totalVolume != null
              ? _formatCurrency(updatedCoin.totalVolume!)
              : 'N/A';

          // Log in requested format
          print(
            '[Realtime Update] Coin: ${updatedCoin.name} | Market Price: $price | Market Cap: $marketCap | Volume: $volume | Time: $timeString',
          );

          _coins[i] = updatedCoin;
          hasUpdates = true;
        }
      }
    }

    // Also update search results if search is active
    if (_isSearchMode && _filteredCoins.isNotEmpty) {
      _updateSearchResultsRealTime(updatedCoins);
    }

    // Check for new coins
    _checkForNewCoinsRealTime(updatedCoins);

    if (hasUpdates) {
      setState(() {
        _filteredCoins = List.from(_coins);
      });
    }
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return '${(price / 1000).toStringAsFixed(1)}K';
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(6);
    }
  }

  void _updateSearchResultsRealTime(List<CoinData> updatedCoins) {
    final now = DateTime.now();
    final timeString =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}';

    print(
      '🔍 Real-time update for search results: ${_filteredCoins.length} items at $timeString',
    );

    bool hasSearchUpdates = false;

    for (int i = 0; i < _filteredCoins.length; i++) {
      final currentCoin = _filteredCoins[i];
      final updatedCoin = updatedCoins.firstWhere(
        (coin) => coin.id == currentCoin.id,
        orElse: () => currentCoin,
      );

      if (updatedCoin.currentPrice != currentCoin.currentPrice ||
          updatedCoin.marketCap != currentCoin.marketCap ||
          updatedCoin.totalVolume != currentCoin.totalVolume) {
        // Format values for logging
        final price = updatedCoin.currentPrice != null
            ? '\$${_formatPrice(updatedCoin.currentPrice!)}'
            : 'N/A';
        final marketCap = updatedCoin.marketCap != null
            ? _formatCurrency(updatedCoin.marketCap!)
            : 'N/A';
        final volume = updatedCoin.totalVolume != null
            ? _formatCurrency(updatedCoin.totalVolume!)
            : 'N/A';

        // Log in requested format for search results
        print(
          '[Realtime Update - Search] Coin: ${updatedCoin.name} | Market Price: $price | Market Cap: $marketCap | Volume: $volume | Time: $timeString',
        );

        _filteredCoins[i] = updatedCoin;
        hasSearchUpdates = true;
      }
    }

    if (hasSearchUpdates) {
      print(
        '🔍✅ Search results updated with new prices/volumes at $timeString',
      );
    }
  }

  void _checkForNewCoinsRealTime(List<CoinData> latestCoins) {
    final now = DateTime.now();
    final timeString =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}';

    final existingIds = _coins.map((coin) => coin.id).toSet();
    final newCoins = latestCoins
        .where((coin) => !existingIds.contains(coin.id))
        .toList();

    if (newCoins.isNotEmpty) {
      print('🆕 Found ${newCoins.length} new coins to add at $timeString');

      for (final newCoin in newCoins) {
        final price = newCoin.currentPrice != null
            ? '\$${_formatPrice(newCoin.currentPrice!)}'
            : 'N/A';
        final marketCap = newCoin.marketCap != null
            ? _formatCurrency(newCoin.marketCap!)
            : 'N/A';
        final volume = newCoin.totalVolume != null
            ? _formatCurrency(newCoin.totalVolume!)
            : 'N/A';

        print(
          '[New Coin Added] Coin: ${newCoin.name} | Market Price: $price | Market Cap: $marketCap | Volume: $volume | Time: $timeString',
        );
      }

      setState(() {
        // Add new coins at the beginning with proper indexing
        for (final newCoin in newCoins.reversed) {
          _coins.insert(0, newCoin);
        }
        _filteredCoins = List.from(_coins);
        _totalItems = _coins.length;
      });

      // Show notification for new coins
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('🆕 ${newCoins.length} new coins added!'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
            action: SnackBarAction(
              label: 'View',
              textColor: Colors.white,
              onPressed: () {
                setState(() {
                  _currentPage = 1; // Go to first page to see new coins
                });
              },
            ),
          ),
        );
      }
    }
  }
  
  Future<void> _searchCoins(String query) async {
    if (query.isEmpty) {
      setState(() {
        _isSearchMode = false;
        _currentSearchQuery = '';
        _currentPage = 1;
      });

      // When clearing search, reload normal coin list and restart subscriptions
      if (_useSupabaseData) {
        if (_isPageVisible) {
          debugPrint(
            '🔄 Clearing search - restarting normal coin loading with subscriptions',
          );
          await _supabaseCoinService.initializeRealTimeSubscription();
        }
        _loadSupabaseCoins();
      } else {
        _loadTrendingCoins();
      }
      return;
    }

    setState(() {
      _isLoading = true;
      _error = '';
      _isSearchMode = true;
      _currentSearchQuery = query;
      _currentPage = 1;
    });

    try {
      List<CoinData> coins;
      if (_useSupabaseData) {
        // During search, don't use real-time subscriptions to save bandwidth
        // Just get the search results directly
        coins = await _supabaseCoinService.searchCoins(query);
        debugPrint(
          '🔍 Search completed: ${coins.length} coins found for "$query"',
        );
      } else {
        coins = await CoinGeckoService.searchCoins(query);
      }

      setState(() {
        _coins = coins;
        _filteredCoins = coins;
        _totalItems = _filteredCoins.length;
        _updateVisibleCoins();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _pasteFromClipboard() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      if (clipboardData?.text != null && clipboardData!.text!.isNotEmpty) {
        _searchController.text = clipboardData.text!;
        _searchCoins(clipboardData.text!);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to paste from clipboard'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Desktop pagination methods
  void _goToPage(int page) {
    if (page >= 1 && page <= _getTotalPages()) {
      setState(() {
        _currentPage = page;
        _updateVisibleCoins();
      });
    }
  }

  void _previousPage() {
    if (_currentPage > 1) {
      _goToPage(_currentPage - 1);
    }
  }

  void _nextPage() {
    if (_currentPage < _getTotalPages()) {
      _goToPage(_currentPage + 1);
    }
  }

  int _getTotalPages() {
    return (_totalItems / _itemsPerPage).ceil();
  }

  Future<void> _copyToClipboard(String text) async {
    try {
      await Clipboard.setData(ClipboardData(text: text));
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Copied: ${text.length > 20 ? '${text.substring(0, 20)}...' : text}',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to copy to clipboard'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _formatDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return 'N/A';
    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inMinutes < 1) {
        return 'Just now';
      } else if (difference.inMinutes < 60) {
        return '${difference.inMinutes}m ago';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}h ago';
      } else if (difference.inDays < 7) {
        return '${difference.inDays}d ago';
      } else {
        return '${date.day}/${date.month}/${date.year}';
      }
    } catch (e) {
      return 'Invalid date';
    }
  }

  @override
  Widget build(BuildContext context) {
    final isWeb = kIsWeb || MediaQuery.of(context).size.width >= 1024;

    if (isWeb) {
      // Web-first design for desktop
      return Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        appBar: _buildWebAppBar(context),
        body: _buildWebBody(context),
      );
    } else {
      // Original mobile design
      return Scaffold(
        appBar: _buildMobileAppBar(context),
        body: _buildMobileBody(context),
      );
    }
  }

  // Web-specific methods
  PreferredSizeWidget _buildWebAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      title: _buildWebTitle(),
      actions: _buildWebAppBarActions(context),
    );
  }

  Widget _buildWebBody(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading coins...'),
          ],
        ),
      );
    }

    if (_error.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'Error loading coins',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _error,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _useSupabaseData
                  ? _loadSupabaseCoins()
                  : _loadTrendingCoins(),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    // For web, show all coins without search filtering in the main view
    return Column(
      children: [
        Expanded(child: _buildWebTable()),
        _buildWebPaginationControls(),
      ],
    );
  }

  List<Widget> _buildWebAppBarActions(BuildContext context) {
    return [
      // Search button
      IconButton(
        icon: const Icon(LucideIcons.search),
        onPressed: () => _showSearchDialog(context),
        tooltip: 'Search coins',
      ),
      // Filter button
      IconButton(
        icon: const Icon(LucideIcons.listFilter),
        onPressed: _showFilterModal,
        tooltip: 'Filter coins',
      ),
      const SizedBox(width: 8),
    ];
  }

  // Mobile-specific methods
  PreferredSizeWidget _buildMobileAppBar(BuildContext context) {
    return AppBar(
      surfaceTintColor: Colors.transparent,
      title: Row(
        children: [
          const SizedBox(width: 8),
          Image.asset(
            'assets/images/logo.png',
            width: 35,
            height: 35,
            fit: BoxFit.cover,
          ),
          const SizedBox(width: 16),
          const Text('DexTrip', style: TextStyle(fontWeight: FontWeight.bold)),
          if (_useSupabaseData) ...[
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.green.withAlpha(51),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green, width: 1),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    decoration: const BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 4),
                  const Text(
                    'LIVE',
                    style: TextStyle(
                      color: Colors.green,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
      centerTitle: false,
      backgroundColor: Colors.transparent,
      elevation: 0,
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 16.0),
          child: IconButton(
            icon: const Icon(LucideIcons.listFilter),
            onPressed: _showFilterModal,
          ),
        ),
      ],
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(60),
        child: Padding(
          padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
          child: SizedBox(
            height: 45,
            child: SearchBar(
              controller: _searchController,
              hintText: 'Search Coin Name or Token',
              elevation: const WidgetStatePropertyAll(0),
              backgroundColor: WidgetStatePropertyAll(
                Colors.blueGrey.withValues(alpha: 0.1),
              ),
              padding: const WidgetStatePropertyAll(
                EdgeInsets.symmetric(horizontal: 12),
              ),
              constraints: const BoxConstraints(maxHeight: 32),
              leading: const Icon(LucideIcons.search, size: 20),
              trailing: [
                IconButton(
                  icon: const Icon(LucideIcons.clipboard, size: 20),
                  onPressed: _pasteFromClipboard,
                  tooltip: 'Paste from clipboard',
                ),
                if (_searchController.text.isNotEmpty)
                  IconButton(
                    icon: const Icon(LucideIcons.x, size: 20),
                    onPressed: () {
                      _searchController.clear();
                      if (_useSupabaseData) {
                        _loadSupabaseCoins();
                      } else {
                        _loadTrendingCoins();
                      }
                    },
                  ),
              ],
              onSubmitted: _searchCoins,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileBody(BuildContext context) {
    return Column(
      children: [
        if (_isSearchMode && _currentSearchQuery.isNotEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            color: Colors.blue.withAlpha(26),
            child: Row(
              children: [
                Icon(LucideIcons.search, size: 16, color: Colors.blue[600]),
                const SizedBox(width: 8),
                Text(
                  'Searching for "${_currentSearchQuery}" • Real-time updates active',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_totalItems} results',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue[600],
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        Expanded(child: _buildOriginalBody()),
        if (_isDesktop()) _buildPaginationControls(),
      ],
    );
  }

  Widget _buildOriginalBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(LucideIcons.x, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'Error loading coins',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[700],
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _error,
                textAlign: TextAlign.center,
                style: const TextStyle(color: Colors.grey),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _useSupabaseData
                  ? _loadSupabaseCoins()
                  : _loadTrendingCoins(),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_filteredCoins.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(LucideIcons.search, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No coins found',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Try searching with different keywords',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    // Show table layout on desktop, mobile list on mobile
    return _isDesktop() ? _buildDesktopTable() : _buildMobileList();
  }

  PreferredSizeWidget _buildAppBar(BuildContext context, bool isWeb) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      title: isWeb ? _buildWebTitle() : _buildMobileTitle(),
      actions: _buildAppBarActions(context, isWeb),
    );
  }

  Widget _buildWebTitle() {
    return Row(
      children: [
        const Text(
          'Coins',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(width: 16),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Theme.of(
                context,
              ).colorScheme.primary.withValues(alpha: 0.3),
            ),
          ),
          child: Text(
            '${_totalItems} coins',
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMobileTitle() {
    return const Text(
      'Coins',
      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
    );
  }

  List<Widget> _buildAppBarActions(BuildContext context, bool isWeb) {
    return [
      // Search button
      IconButton(
        icon: const Icon(LucideIcons.search),
        onPressed: () => _showSearchDialog(context),
        tooltip: 'Search coins',
      ),
      // Filter button
      IconButton(
        icon: const Icon(LucideIcons.listFilter),
        onPressed: _showFilterModal,
        tooltip: 'Filter coins',
      ),
      const SizedBox(width: 8),
    ];
  }

  Widget _buildWebTable() {
    // For web, show all coins with pagination (no search filtering in main view)
    final coinsToShow = _coins.isNotEmpty ? _coins : [];
    final startIndex = (_currentPage - 1) * _itemsPerPage;
    final endIndex = (startIndex + _itemsPerPage).clamp(0, coinsToShow.length);
    final paginatedCoins = coinsToShow.sublist(startIndex, endIndex);

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          _buildWebTableHeader(),
          Expanded(
            child: ListView.builder(
              itemCount: paginatedCoins.length,
              itemBuilder: (context, index) =>
                  _buildWebTableRow(paginatedCoins[index], index),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWebTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.5),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          // Rank
          const SizedBox(
            width: 60,
            child: Text(
              '#',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ),
          // Symbol with copy icon (left-aligned)
          const Expanded(
            flex: 2,
            child: Text(
              'Coin',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
            ),
          ),
          // Price (center-aligned)
          const Expanded(
            child: Text(
              'Price',
              textAlign: TextAlign.center,
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
            ),
          ),
          // Market Cap (center-aligned)
          const Expanded(
            child: Text(
              'Market Cap',
              textAlign: TextAlign.center,
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
            ),
          ),
          // Volume (center-aligned)
          const Expanded(
            child: Text(
              'Volume',
              textAlign: TextAlign.center,
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
            ),
          ),
          // Created At (center-aligned)
          const Expanded(
            child: Text(
              'Created At',
              textAlign: TextAlign.center,
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWebTableRow(CoinData coin, int index) {
    final globalIndex = (_currentPage - 1) * _itemsPerPage + index;

    return InkWell(
      onTap: () => _navigateToCoinDetail(coin),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Theme.of(context).dividerColor.withValues(alpha: 0.1),
            ),
          ),
        ),
        child: Row(
          children: [
            // Rank
            SizedBox(
              width: 60,
              child: Center(
                child: Text(
                  "${globalIndex + 1}",
                  style: TextStyle(
                    color: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            // Coin info with copy icon
            Expanded(
              flex: 2,
              child: Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.1)
                          : Colors.grey.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: coin.image != null && coin.image!.isNotEmpty
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(16),
                            child: Image.network(
                              coin.image!,
                              width: 32,
                              height: 32,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Center(child: SizedBox()),
                            ),
                          )
                        : Center(
                            child: Text(
                              coin.symbol.isNotEmpty
                                  ? coin.symbol.substring(0, 1).toUpperCase()
                                  : "",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                                color: Theme.of(
                                  context,
                                ).textTheme.bodyLarge?.color,
                              ),
                            ),
                          ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min, // Prevent overflow
                      children: [
                        Row(
                          children: [
                            Flexible(
                              child: Text(
                                coin.symbol.toUpperCase(),
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 8),
                            if (coin.tokenAddress != null)
                              IconButton(
                                icon: const Icon(LucideIcons.copy, size: 14),
                                onPressed: () =>
                                    _copyToClipboard(coin.tokenAddress!),
                                tooltip: 'Copy address',
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(
                                  minWidth: 20,
                                  minHeight: 20,
                                ),
                              ),
                          ],
                        ),
                        Flexible(
                          child: Text(
                            coin.name,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Market Price
            Expanded(
              child: Text(
                coin.formattedPrice.isNotEmpty ? coin.formattedPrice : '\$0.00',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ),
            // Market Cap
            Expanded(
              child: Text(
                coin.marketCap != null
                    ? _formatCurrency(coin.marketCap!)
                    : 'N/A',
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 14),
              ),
            ),
            // Volume
            Expanded(
              child: Text(
                coin.totalVolume != null
                    ? _formatCurrency(coin.totalVolume!)
                    : 'N/A',
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 14),
              ),
            ),
            // Created At
            Expanded(
              child: Text(
                _formatDate(coin.lastUpdated),
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatCurrency(double value) {
    if (value >= 1e12) {
      return '\$${(value / 1e12).toStringAsFixed(2)}T';
    } else if (value >= 1e9) {
      return '\$${(value / 1e9).toStringAsFixed(2)}B';
    } else if (value >= 1e6) {
      return '\$${(value / 1e6).toStringAsFixed(2)}M';
    } else if (value >= 1e3) {
      return '\$${(value / 1e3).toStringAsFixed(2)}K';
    } else {
      return '\$${value.toStringAsFixed(2)}';
    }
  }

  Widget _buildBody(BuildContext context, bool isWeb) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading coins...'),
          ],
        ),
      );
    }

    if (_error.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'Error loading coins',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _error,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _useSupabaseData
                  ? _loadSupabaseCoins()
                  : _loadTrendingCoins(),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return isWeb ? _buildWebLayout() : _buildMobileLayout();
  }

  Widget _buildWebLayout() {
    return Column(
      children: [
        Expanded(child: _buildWebTable()),
        _buildPaginationControls(),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return RefreshIndicator(
      onRefresh: () =>
          _useSupabaseData ? _loadSupabaseCoins() : _loadTrendingCoins(),
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: _visibleCoins.length,
        itemBuilder: (context, index) {
          final coin = _visibleCoins[index];

          // Create or get key for visibility tracking
          _coinKeys[coin.id] ??= GlobalKey();

          return Container(
            key: _coinKeys[coin.id],
            child: _buildMobileCoinCard(coin),
          );
        },
      ),
    );
  }

  Widget _buildMobileCoinCard(CoinData coin) {
    final isPositive =
        coin.priceChangePercentage24h != null &&
        coin.priceChangePercentage24h! >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _navigateToCoinDetail(coin),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Coin image
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  color: Theme.of(context).colorScheme.surface,
                ),
                child: coin.image != null && coin.image!.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: Image.network(
                          coin.image!,
                          width: 40,
                          height: 40,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Center(
                            child: Text(
                              coin.symbol.isNotEmpty
                                  ? coin.symbol.substring(0, 1).toUpperCase()
                                  : "?",
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      )
                    : Center(
                        child: Text(
                          coin.symbol.isNotEmpty
                              ? coin.symbol.substring(0, 1).toUpperCase()
                              : "?",
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ),
              ),
              const SizedBox(width: 12),
              // Coin info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      coin.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      coin.symbol.toUpperCase(),
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                  ],
                ),
              ),
              // Price and change
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    coin.formattedPrice.isNotEmpty
                        ? coin.formattedPrice
                        : '\$0.00',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  if (coin.priceChangePercentage24h != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: changeColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${isPositive ? '+' : ''}${coin.priceChangePercentage24h!.toStringAsFixed(2)}%',
                        style: TextStyle(
                          color: changeColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToCoinDetail(CoinData coin) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CoinDetailPage(symbol: coin.symbol, coin: coin),
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    final TextEditingController dialogSearchController =
        TextEditingController();
    List<CoinData> searchResults = [];
    String currentQuery = '';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.9,
            height: MediaQuery.of(context).size.height * 0.9,
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                // Search header
                Row(
                  children: [
                    const Icon(LucideIcons.search, size: 24),
                    const SizedBox(width: 12),
                    const Text(
                      'Search Coins',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(LucideIcons.x),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // Search input
                TextField(
                  controller: dialogSearchController,
                  decoration: InputDecoration(
                    hintText: 'Search by name, symbol, or address...',
                    prefixIcon: const Icon(LucideIcons.search),
                    suffixIcon: dialogSearchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(LucideIcons.x),
                            onPressed: () {
                              dialogSearchController.clear();
                              setDialogState(() {
                                searchResults = [];
                                currentQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onChanged: (value) {
                    setDialogState(() {
                      currentQuery = value;
                      if (value.isNotEmpty) {
                        searchResults = _filterCoins(_coins, value);
                      } else {
                        searchResults = [];
                      }
                    });
                  },
                ),
                const SizedBox(height: 16),
                // Search message
                if (currentQuery.isNotEmpty)
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(
                        context,
                      ).colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Theme.of(
                          context,
                        ).colorScheme.primary.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      'Searching for "$currentQuery" — ${searchResults.length} coins found',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                if (currentQuery.isNotEmpty) const SizedBox(height: 16),
                // Results in table format
                Expanded(
                  child: currentQuery.isEmpty
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                LucideIcons.search,
                                size: 48,
                                color: Colors.grey,
                              ),
                              SizedBox(height: 16),
                              Text(
                                'Start typing to search coins...',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        )
                      : searchResults.isEmpty
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                LucideIcons.search,
                                size: 48,
                                color: Colors.grey,
                              ),
                              SizedBox(height: 16),
                              Text(
                                'No coins found',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        )
                      : _buildSearchResultsTable(searchResults),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSearchResultsTable(List<CoinData> searchResults) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          // Table header (same as main table)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.surface.withValues(alpha: 0.5),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                // Rank
                const SizedBox(
                  width: 60,
                  child: Text(
                    '#',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                ),
                // Coin with copy icon (left-aligned)
                const Expanded(
                  flex: 2,
                  child: Text(
                    'Coin',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                ),
                // Price (center-aligned)
                const Expanded(
                  child: Text(
                    'Price',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                ),
                // Market Cap (center-aligned)
                const Expanded(
                  child: Text(
                    'Market Cap',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                ),
                // Volume (center-aligned)
                const Expanded(
                  child: Text(
                    'Volume',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                ),
                // Created At (center-aligned)
                const Expanded(
                  child: Text(
                    'Created At',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                ),
              ],
            ),
          ),
          // Search results
          Expanded(
            child: ListView.builder(
              itemCount: searchResults.length,
              itemBuilder: (context, index) {
                final coin = searchResults[index];
                return InkWell(
                  onTap: () {
                    Navigator.pop(context);
                    _navigateToCoinDetail(coin);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Theme.of(
                            context,
                          ).dividerColor.withValues(alpha: 0.1),
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        // Rank
                        SizedBox(
                          width: 60,
                          child: Center(
                            child: Text(
                              "${index + 1}",
                              style: TextStyle(
                                color: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.color
                                    ?.withValues(alpha: 0.7),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                        // Coin info with copy icon
                        Expanded(
                          flex: 2,
                          child: Row(
                            children: [
                              Container(
                                width: 32,
                                height: 32,
                                decoration: BoxDecoration(
                                  color:
                                      Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? Colors.white.withValues(alpha: 0.1)
                                      : Colors.grey.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child:
                                    coin.image != null && coin.image!.isNotEmpty
                                    ? ClipRRect(
                                        borderRadius: BorderRadius.circular(16),
                                        child: Image.network(
                                          coin.image!,
                                          width: 32,
                                          height: 32,
                                          fit: BoxFit.cover,
                                          errorBuilder:
                                              (context, error, stackTrace) =>
                                                  Center(child: SizedBox()),
                                        ),
                                      )
                                    : Center(
                                        child: Text(
                                          coin.symbol.isNotEmpty
                                              ? coin.symbol
                                                    .substring(0, 1)
                                                    .toUpperCase()
                                              : "",
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                            color: Theme.of(
                                              context,
                                            ).textTheme.bodyLarge?.color,
                                          ),
                                        ),
                                      ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Text(
                                          coin.symbol.toUpperCase(),
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 14,
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        if (coin.tokenAddress != null)
                                          IconButton(
                                            icon: const Icon(
                                              LucideIcons.copy,
                                              size: 14,
                                            ),
                                            onPressed: () => _copyToClipboard(
                                              coin.tokenAddress!,
                                            ),
                                            tooltip: 'Copy address',
                                            padding: EdgeInsets.zero,
                                            constraints: const BoxConstraints(
                                              minWidth: 20,
                                              minHeight: 20,
                                            ),
                                          ),
                                      ],
                                    ),
                                    Text(
                                      coin.name,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Market Price
                        Expanded(
                          child: Text(
                            coin.formattedPrice.isNotEmpty
                                ? coin.formattedPrice
                                : '\$0.00',
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        // Market Cap
                        Expanded(
                          child: Text(
                            coin.marketCap != null
                                ? _formatCurrency(coin.marketCap!)
                                : 'N/A',
                            textAlign: TextAlign.center,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                        // Volume
                        Expanded(
                          child: Text(
                            coin.totalVolume != null
                                ? _formatCurrency(coin.totalVolume!)
                                : 'N/A',
                            textAlign: TextAlign.center,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                        // Created At
                        Expanded(
                          child: Text(
                            _formatDate(coin.lastUpdated),
                            textAlign: TextAlign.center,
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWebPaginationControls() {
    final totalCoins = _coins.length;
    final totalPages = (totalCoins / _itemsPerPage).ceil();
    final startItem = (_currentPage - 1) * _itemsPerPage + 1;
    final endItem = (_currentPage * _itemsPerPage).clamp(0, totalCoins);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Showing $startItem-$endItem of $totalCoins coins',
            style: TextStyle(
              color: Theme.of(
                context,
              ).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),
          Row(
            children: [
              ElevatedButton(
                onPressed: _currentPage > 1 ? _goToPreviousPage : null,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                ),
                child: const Text('Previous'),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: Theme.of(context).dividerColor),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Page $_currentPage of $totalPages',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: _currentPage < totalPages ? _goToNextPage : null,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                ),
                child: const Text('Next'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _goToPreviousPage() {
    if (_currentPage > 1) {
      setState(() {
        _currentPage--;
      });
      print('📄 Navigated to page $_currentPage');
      // Update real-time subscription for new visible items
      _updateVisibleCoinsRealTime(_coins);
    }
  }

  void _goToNextPage() {
    final totalPages = (_coins.length / _itemsPerPage).ceil();
    if (_currentPage < totalPages) {
      setState(() {
        _currentPage++;
      });
      print('📄 Navigated to page $_currentPage');
      // Update real-time subscription for new visible items
      _updateVisibleCoinsRealTime(_coins);
    }
  }

  Widget _buildMobileList() {
    return RefreshIndicator(
      onRefresh: () =>
          _useSupabaseData ? _loadSupabaseCoins() : _loadTrendingCoins(),
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        itemCount: _visibleCoins.length,
        itemBuilder: (context, index) {
          final coin = _visibleCoins[index];
          return _buildMobileCoinTile(coin, index);
        },
      ),
    );
  }

  Widget _buildMobileCoinTile(CoinData coin, int index) {
    final isPositive =
        coin.market_cap_change_percent != null &&
        coin.market_cap_change_percent! >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;
    final subtitleColor = Theme.of(context).brightness == Brightness.dark
        ? Colors.grey[400]
        : Colors.grey[600];

    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  CoinDetailPage(coin: coin, symbol: coin.symbol),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 11, vertical: 12),
          child: Row(
            spacing: 5,
            children: [
              // Coin Icon and Rank
              Text(
                "${index + 1}",
                style: TextStyle(
                  color: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 3),
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.1)
                      : Colors.grey.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: coin.image != null && coin.image!.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.network(
                          coin.image!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              Center(child: SizedBox()),
                        ),
                      )
                    : Center(
                        child: Text(
                          coin.symbol.isNotEmpty
                              ? coin.symbol.substring(0, 1).toUpperCase()
                              : "",
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                          ),
                        ),
                      ),
              ),

              const SizedBox(width: 8),
              // Coin Name and Symbol
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min, // Prevent overflow
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: coin.displayName.isNotEmpty
                              ? Text(
                                  coin.displayName,
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 15,
                                    color: Theme.of(
                                      context,
                                    ).textTheme.bodyLarge?.color,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                )
                              : Text(
                                  coin.displaySymbol.split(' / ')[0],
                                  style: TextStyle(
                                    fontWeight: FontWeight.w800,
                                    fontSize: 15,
                                    color: Theme.of(
                                      context,
                                    ).textTheme.bodyLarge?.color,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                        ),
                        if (coin.tokenAddress != null &&
                            coin.tokenAddress!.isNotEmpty) ...[
                          const SizedBox(width: 4),
                          GestureDetector(
                            onTap: () => _copyTokenAddress(
                              coin.tokenAddress!,
                              coin.displayName.isNotEmpty
                                  ? coin.displayName
                                  : coin.symbol,
                            ),
                            child: Icon(
                              LucideIcons.copy,
                              size: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 6),
                    Row(
                      spacing: 5,
                      children: [
                        Text(
                          coin.displaySymbol,
                          style: TextStyle(
                            color: subtitleColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                        ),
                        // Coin image
                        if (coin.image != null) ...[
                          Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color:
                                    Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? Colors.white.withValues(alpha: 0.1)
                                    : Colors.grey.withValues(alpha: 0.2),
                                width: 0.5,
                              ),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(10),
                              child: Image.network(
                                coin.image!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) =>
                                    Icon(
                                      LucideIcons.coins,
                                      size: 12,
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.color
                                          ?.withValues(alpha: 0.5),
                                    ),
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),

              // Price and Change
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisSize: MainAxisSize.min, // Prevent overflow
                children: [
                  if (coin.marketCap != null)
                    Text(
                      CurrencyFormatter.format(coin.marketCap),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 15,
                        color: Theme.of(context).textTheme.bodyLarge?.color,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  const SizedBox(height: 4),
                  if (coin.market_cap_change_percent != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: changeColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: changeColor.withValues(alpha: 0.3),
                          width: 0.5,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            isPositive
                                ? Icons.arrow_drop_up
                                : Icons.arrow_drop_down,
                            color: changeColor,
                            size: 14,
                          ),
                          Flexible(
                            child: Text(
                              CurrencyFormatter.formatPercentage(
                                coin.market_cap_change_percent,
                              ),
                              style: TextStyle(
                                color: changeColor,
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDesktopTable() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor.withOpacity(0.1),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildTableHeader(),
          Expanded(
            child: Scrollbar(
              controller: _scrollController,
              child: SingleChildScrollView(
                controller: _scrollController,
                child: Column(
                  children: _visibleCoins.asMap().entries.map((entry) {
                    final index = entry.key;
                    final coin = entry.value;
                    final globalIndex =
                        (_currentPage - 1) * _itemsPerPage + index;

                    // Create or get key for visibility tracking
                    _coinKeys[coin.id] ??= GlobalKey();

                    return Container(
                      key: _coinKeys[coin.id],
                      child: _buildTableRow(coin, globalIndex),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey[850]
            : Colors.grey[100],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          const SizedBox(
            width: 40,
            child: Text('#', style: TextStyle(fontWeight: FontWeight.bold)),
          ),
          const SizedBox(width: 16),
          const Expanded(
            flex: 3,
            child: Text(
              'Symbol',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          const Expanded(
            flex: 2,
            child: Center(
              child: Text(
                'Address',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
          const Expanded(
            flex: 2,
            child: Center(
              child: Text(
                'Price',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
          const Expanded(
            flex: 2,
            child: Center(
              child: Text(
                'Market Cap',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
          const Expanded(
            flex: 2,
            child: Center(
              child: Text(
                'Liquidity',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
          const Expanded(
            flex: 2,
            child: Center(
              child: Text(
                '24h Change',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
          const Expanded(
            flex: 2,
            child: Center(
              child: Text(
                'Updated',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableRow(CoinData coin, int index) {
    final isPositive =
        coin.market_cap_change_percent != null &&
        coin.market_cap_change_percent! >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  CoinDetailPage(coin: coin, symbol: coin.symbol),
            ),
          );
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor.withOpacity(0.1),
              ),
            ),
          ),
          child: Row(
            children: [
              // Rank
              SizedBox(
                width: 40,
                child: Text(
                  "${index + 1}",
                  style: TextStyle(
                    color: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.color?.withOpacity(0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // Symbol with icon
              Expanded(
                flex: 3,
                child: Row(
                  children: [
                    // Coin icon or first letter
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white.withOpacity(0.1)
                            : Colors.grey.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: coin.image != null && coin.image!.isNotEmpty
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(16),
                              child: Image.network(
                                coin.image!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) =>
                                    Center(
                                      child: Text(
                                        coin.symbol
                                            .substring(0, 1)
                                            .toUpperCase(),
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 14,
                                          color: Theme.of(
                                            context,
                                          ).textTheme.bodyLarge?.color,
                                        ),
                                      ),
                                    ),
                              ),
                            )
                          : Center(
                              child: Text(
                                coin.symbol.substring(0, 1).toUpperCase(),
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: Theme.of(
                                    context,
                                  ).textTheme.bodyLarge?.color,
                                ),
                              ),
                            ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min, // Prevent overflow
                        children: [
                          Text(
                            coin.displaySymbol,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (coin.displayName.isNotEmpty)
                            Text(
                              coin.displayName,
                              style: TextStyle(
                                fontSize: 12,
                                color: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.color
                                    ?.withValues(alpha: 0.7),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Address with copy button
              Expanded(
                flex: 2,
                child: Center(
                  child: coin.tokenAddress != null
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Flexible(
                              child: Text(
                                '${coin.tokenAddress!.substring(0, 6)}...${coin.tokenAddress!.substring(coin.tokenAddress!.length - 4)}',
                                style: const TextStyle(
                                  fontFamily: 'monospace',
                                  fontSize: 12,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 4),
                            IconButton(
                              icon: const Icon(LucideIcons.copy, size: 14),
                              onPressed: () =>
                                  _copyToClipboard(coin.tokenAddress!),
                              tooltip: 'Copy address',
                              constraints: const BoxConstraints(
                                minWidth: 24,
                                minHeight: 24,
                              ),
                              padding: const EdgeInsets.all(4),
                            ),
                          ],
                        )
                      : const Text('N/A', style: TextStyle(color: Colors.grey)),
                ),
              ),

              // Price
              Expanded(
                flex: 2,
                child: Center(
                  child: Text(
                    coin.currentPrice != null
                        ? CurrencyFormatter.format(coin.currentPrice)
                        : 'N/A',
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 13,
                    ),
                  ),
                ),
              ),

              // Market Cap
              Expanded(
                flex: 2,
                child: Center(
                  child: Text(
                    coin.marketCap != null
                        ? CurrencyFormatter.format(coin.marketCap)
                        : 'N/A',
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 13,
                    ),
                  ),
                ),
              ),

              // Liquidity
              Expanded(
                flex: 2,
                child: Center(
                  child: Text(
                    coin.totalVolume != null
                        ? CurrencyFormatter.format(coin.totalVolume)
                        : 'N/A',
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 13,
                    ),
                  ),
                ),
              ),

              // 24h Change (pill badge)
              Expanded(
                flex: 2,
                child: Center(
                  child: coin.market_cap_change_percent != null
                      ? Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: changeColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: changeColor.withOpacity(0.3),
                              width: 0.5,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                isPositive
                                    ? Icons.arrow_drop_up
                                    : Icons.arrow_drop_down,
                                color: changeColor,
                                size: 16,
                              ),
                              Text(
                                CurrencyFormatter.formatPercentage(
                                  coin.market_cap_change_percent,
                                ),
                                style: TextStyle(
                                  color: changeColor,
                                  fontSize: 11,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        )
                      : const Text('N/A', style: TextStyle(color: Colors.grey)),
                ),
              ),

              // Updated time
              Expanded(
                flex: 2,
                child: Center(
                  child: Text(
                    _formatDate(coin.lastUpdated),
                    style: TextStyle(
                      fontSize: 11,
                      color: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.color?.withOpacity(0.7),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaginationControls() {
    final totalPages = _getTotalPages();

    if (totalPages <= 1) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor.withOpacity(0.1),
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Showing ${(_currentPage - 1) * _itemsPerPage + 1}-${(_currentPage * _itemsPerPage).clamp(0, _totalItems)} of $_totalItems coins',
            style: TextStyle(
              color: Theme.of(
                context,
              ).textTheme.bodyMedium?.color?.withOpacity(0.7),
              fontSize: 14,
            ),
          ),
          Row(
            children: [
              IconButton(
                onPressed: _currentPage > 1 ? _previousPage : null,
                icon: const Icon(LucideIcons.chevronLeft),
                tooltip: 'Previous page',
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey[800]
                      : Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Page $_currentPage of $totalPages',
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: _currentPage < totalPages ? _nextPage : null,
                icon: const Icon(LucideIcons.chevronRight),
                tooltip: 'Next page',
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showFilterModal() {
       if (ResponsiveHelper.isDesktop(context)) {
      showDialog(
        context: context,
        barrierDismissible: true, 
        builder: (context) => CustomBorderDialog(context, child: Container(width: MediaQuery.of(context).size.width * 0.4,height:MediaQuery.of(context).size.height * 0.8 ,child: CoinFilterModal(
        currentFilters: _filters,
        onFiltersChanged: (newFilters) {
          setState(() {
            _filters = newFilters;
          });
          _applyFilters();
        },
      ),),),
      );
    } else {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      builder: (context) => CoinFilterModal(
        currentFilters: _filters,
        onFiltersChanged: (newFilters) {
          setState(() {
            _filters = newFilters;
          });
          _applyFilters();
        },
      ),
    );
  }
  }

  void _applyFilters() async {
    if (!_useSupabaseData) {
      // For CoinGecko, just show message (filters not implemented)
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Filters applied: ${_filters.length} active filters'),
          backgroundColor: Colors.green,
        ),
      );
      return;
    }

    // Apply filters for Supabase data
    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      final coins = await _supabaseCoinService.getFilteredCoins(
        minMarketCap: _filters['minMarketCap']?.toDouble(),
        maxMarketCap: _filters['maxMarketCap']?.toDouble(),
        minLiquidity: _filters['minLiquidity']?.toDouble(),
        maxLiquidity: _filters['maxLiquidity']?.toDouble(),
        minFdv: _filters['minFdv']?.toDouble(),
        maxFdv: _filters['maxFdv']?.toDouble(),
        dexId: _filters['dexId']?.toString(),
        limit: 100,
      );

      setState(() {
        _coins = coins;
        _filteredCoins = _searchController.text.isEmpty
            ? coins
            : _filterCoins(coins, _searchController.text);
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Found ${coins.length} coins matching filters'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }


}


//   void _applyFilters() async {
//     setState(() {
//       _isLoading = true;
//       _error = '';
//     });

//     try {
//       List<CoinData> filteredCoins = List.from(_coins);

//       // Apply market cap filter
//       if (_filters['minMarketCap'] != null) {
//         final minMarketCap = _filters['minMarketCap'] as double;
//         filteredCoins = filteredCoins.where((coin) {
//           return coin.marketCap != null && coin.marketCap! >= minMarketCap;
//         }).toList();
//       }

//       // Apply volume filter
//       if (_filters['minVolume'] != null) {
//         final minVolume = _filters['minVolume'] as double;
//         filteredCoins = filteredCoins.where((coin) {
//           return coin.totalVolume != null && coin.totalVolume! >= minVolume;
//         }).toList();
//       }

//       setState(() {
//         _filteredCoins = filteredCoins;
//         _totalItems = _filteredCoins.length;
//         _currentPage = 1;
//         _isLoading = false;
//         _error = '';
//       });

//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(
//           content: Text(
//             'Found ${filteredCoins.length} coins matching your filters',
//           ),
//           backgroundColor: Colors.green,
//         ),
//       );
//     } catch (e) {
//       setState(() {
//         _error = 'Failed to apply filters: $e';
//         _isLoading = false;
//       });

//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(
//           content: Text('Error applying filters: $e'),
//           backgroundColor: Colors.red,
//         ),
//       );
//     }
//   }
// }
