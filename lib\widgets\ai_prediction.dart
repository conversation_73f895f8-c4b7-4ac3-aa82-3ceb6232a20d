import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../theme/app_theme.dart';

class AIPrediction extends StatefulWidget {
  final String symbol;
  final double currentPrice;

  const AIPrediction({
    super.key,
    required this.symbol,
    required this.currentPrice,
  });

  @override
  State<AIPrediction> createState() => _AIPredictionState();
}

class _AIPredictionState extends State<AIPrediction>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  bool _isAnalyzing = true;
  Timer? _analysisTimer;

  // AI Prediction data
  String _trend = 'BULLISH';
  double _confidence = 0.0;
  double _predictedPrice24h = 0.0;
  double _predictedPrice7d = 0.0;
  List<String> _signals = [];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAIAnalysis();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _analysisTimer?.cancel();
    super.dispose();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _pulseController.repeat(reverse: true);
  }

  void _startAIAnalysis() {
    setState(() {
      _isAnalyzing = true;
    });

    // Simulate AI analysis with realistic delay
    _analysisTimer = Timer(const Duration(seconds: 3), () {
      _generateAIPrediction();
    });
  }

  void _generateAIPrediction() {
    final random = Random();

    // Generate realistic AI predictions
    final trends = ['BULLISH', 'BEARISH', 'NEUTRAL'];

    setState(() {
      _isAnalyzing = false;
      _trend = trends[random.nextInt(trends.length)];
      _confidence = 0.65 + random.nextDouble() * 0.3; // 65-95% confidence

      // Generate price predictions based on trend
      final multiplier = _trend == 'BULLISH'
          ? 1.05 + random.nextDouble() * 0.1
          : _trend == 'BEARISH'
          ? 0.9 + random.nextDouble() * 0.1
          : 0.98 + random.nextDouble() * 0.04;

      _predictedPrice24h = widget.currentPrice * multiplier;
      _predictedPrice7d =
          widget.currentPrice *
          (multiplier + (random.nextDouble() - 0.5) * 0.1);

      // Generate AI signals
      _signals = _generateSignals();
    });
  }

  List<String> _generateSignals() {
    final allSignals = [
      'Strong volume increase detected',
      'RSI indicates oversold conditions',
      'Moving averages showing bullish crossover',
      'Social sentiment trending positive',
      'Whale activity increasing',
      'Technical resistance broken',
      'Support level holding strong',
      'Market correlation analysis favorable',
      'On-chain metrics improving',
      'Trading volume above average',
    ];

    final random = Random();
    final signalCount = 3 + random.nextInt(3); // 3-5 signals
    final selectedSignals = <String>[];

    while (selectedSignals.length < signalCount &&
        selectedSignals.length < allSignals.length) {
      final signal = allSignals[random.nextInt(allSignals.length)];
      if (!selectedSignals.contains(signal)) {
        selectedSignals.add(signal);
      }
    }

    return selectedSignals;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _isAnalyzing ? _pulseAnimation.value : 1.0,
                      child: Icon(
                        LucideIcons.brain,
                        color: AppTheme.primaryColor,
                        size: 20,
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'AI Prediction',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      _isAnalyzing
                          ? 'Analyzing market data...'
                          : 'Analysis complete',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
              if (!_isAnalyzing)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getTrendColor().withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _trend,
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: _getTrendColor(),
                    ),
                  ),
                ),
            ],
          ),

          if (_isAnalyzing) ...[
            const SizedBox(height: 24),
            _buildAnalyzingState(),
          ] else ...[
            const SizedBox(height: 20),
            _buildPredictionContent(),
          ],
        ],
      ),
    );
  }

  Widget _buildAnalyzingState() {
    return Container(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: double.infinity,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(2),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(2),
              child: LinearProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppTheme.primaryColor,
                ),
                backgroundColor: Colors.transparent,
              ),
            ),
          ),
          // const SizedBox(height: 16),
          // Text(
          //   'Processing market indicators...',
          //   style: TextStyle(
          //     color: Colors.white.withValues(alpha: 0.6),
          //     fontSize: 14,
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _buildPredictionContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Confidence meter
        Row(
          children: [
            Text(
              'Confidence: ',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.6),
                fontSize: 14,
              ),
            ),
            Expanded(
              child: Container(
                height: 6,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(3),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: _confidence,
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '${(_confidence * 100).toInt()}%',
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Price predictions
        Row(
          children: [
            Expanded(
              child: _buildPredictionCard(
                '24h Prediction',
                _predictedPrice24h,
                widget.currentPrice,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildPredictionCard(
                '7d Prediction',
                _predictedPrice7d,
                widget.currentPrice,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // AI Signals
        Text(
          'Key Signals',
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),

        const SizedBox(height: 8),

        ..._signals
            .take(3)
            .map(
              (signal) => Padding(
                padding: const EdgeInsets.only(bottom: 6),
                child: Row(
                  children: [
                    Container(
                      width: 4,
                      height: 4,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        signal,
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.7),
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            )
            .toList(),
      ],
    );
  }

  Widget _buildPredictionCard(
    String title,
    double predictedPrice,
    double currentPrice,
  ) {
    final change = ((predictedPrice - currentPrice) / currentPrice) * 100;
    final isPositive = change >= 0;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '\$${predictedPrice.toStringAsFixed(predictedPrice < 1 ? 4 : 2)}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 2),
          Row(
            children: [
              Icon(
                isPositive ? LucideIcons.trendingUp : LucideIcons.trendingDown,
                color: isPositive ? Colors.green : Colors.red,
                size: 12,
              ),
              const SizedBox(width: 4),
              Text(
                '${isPositive ? '+' : ''}${change.toStringAsFixed(1)}%',
                style: TextStyle(
                  color: isPositive ? Colors.green : Colors.red,
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getTrendColor() {
    switch (_trend) {
      case 'BULLISH':
        return Colors.green;
      case 'BEARISH':
        return Colors.red;
      default:
        return Colors.orange;
    }
  }
}
