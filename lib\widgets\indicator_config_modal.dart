import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../theme/app_theme.dart';
import '../widgets/gradient_button.dart';

class TradingIndicator {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  bool isEnabled;

  TradingIndicator({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    this.isEnabled = false,
  });
}

class IndicatorConfigModal extends StatefulWidget {
  final String coinSymbol;
  final VoidCallback? onStartBot;

  const IndicatorConfigModal({
    super.key,
    required this.coinSymbol,
    this.onStartBot,
  });

  @override
  State<IndicatorConfigModal> createState() => _IndicatorConfigModalState();
}

class _IndicatorConfigModalState extends State<IndicatorConfigModal> {
  late List<TradingIndicator> _indicators;

  @override
  void initState() {
    super.initState();
    _indicators = [
      TradingIndicator(
        id: 'rsi',
        name: 'RSI',
        description: 'Relative Strength Index - Momentum oscillator',
        icon: LucideIcons.trendingUp,
        isEnabled: true, // Default enabled
      ),
      TradingIndicator(
        id: 'macd',
        name: 'MACD',
        description: 'Moving Average Convergence Divergence',
        icon: LucideIcons.activity,
        isEnabled: true, // Default enabled
      ),
      TradingIndicator(
        id: 'bollinger',
        name: 'Bollinger Bands',
        description: 'Volatility indicator with upper and lower bands',
        icon: LucideIcons.activity,
        isEnabled: false,
      ),
      TradingIndicator(
        id: 'sma',
        name: 'SMA',
        description: 'Simple Moving Average - Trend following indicator',
        icon: LucideIcons.trendingUp,
        isEnabled: false,
      ),
      TradingIndicator(
        id: 'ema',
        name: 'EMA',
        description: 'Exponential Moving Average - Responsive trend indicator',
        icon: LucideIcons.trendingUp,
        isEnabled: true, // Default enabled
      ),
      TradingIndicator(
        id: 'stochastic',
        name: 'Stochastic',
        description:
            'Momentum indicator comparing closing price to price range',
        icon: LucideIcons.zap,
        isEnabled: false,
      ),
      TradingIndicator(
        id: 'volume',
        name: 'Volume',
        description: 'Trading volume analysis',
        icon: LucideIcons.activity,
        isEnabled: true, // Default enabled
      ),
      TradingIndicator(
        id: 'support_resistance',
        name: 'Support/Resistance',
        description: 'Key price levels identification',
        icon: LucideIcons.minus,
        isEnabled: false,
      ),
    ];
  }

  void _toggleIndicator(int index) {
    setState(() {
      _indicators[index].isEnabled = !_indicators[index].isEnabled;
    });
  }

  void _startBot() {
    Navigator.pop(context);
    widget.onStartBot?.call();
    _showBotStartedSnackbar();
  }

  void _showBotStartedSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(LucideIcons.check, color: Colors.green, size: 20),
            const SizedBox(width: 12),
            Text(
              'Trading bot started for ${widget.coinSymbol.toUpperCase()}',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green.withValues(alpha: 0.9),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Color(0xFF1A1A1A),
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[600],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    LucideIcons.settings,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Configure Trading Bot',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        'Set up indicators for ${widget.coinSymbol.toUpperCase()}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(
                    LucideIcons.x,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),

          // Indicators list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemCount: _indicators.length,
              itemBuilder: (context, index) {
                final indicator = _indicators[index];

                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2A2A2A),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        // Switch button on the left
                        Switch(
                          value: indicator.isEnabled,
                          onChanged: (value) => _toggleIndicator(index),
                          activeColor: AppTheme.primaryColor,
                          activeTrackColor: AppTheme.primaryColor.withValues(
                            alpha: 0.3,
                          ),
                          inactiveThumbColor: Colors.white.withValues(
                            alpha: 0.6,
                          ),
                          inactiveTrackColor: Colors.white.withValues(
                            alpha: 0.1,
                          ),
                        ),

                        const SizedBox(width: 16),

                        // Title and subtitle
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                indicator.name,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                indicator.description,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.white.withValues(alpha: 0.6),
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Chevron right
                        GestureDetector(
                          onTap: () => _showIndicatorDetails(indicator),
                          child: Icon(
                            LucideIcons.chevronRight,
                            color: Colors.white.withValues(alpha: 0.4),
                            size: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),

          // Start Bot Button
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                GradientButton(
                  text: 'Start Trading Bot',
                  onPressed: _startBot,
                  icon: LucideIcons.play,
                ),
                const SizedBox(height: 8),
                Text(
                  'Bot will start trading based on your configuration',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.6),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showIndicatorDetails(TradingIndicator indicator) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildIndicatorDetailsModal(indicator),
    );
  }

  Widget _buildIndicatorDetailsModal(TradingIndicator indicator) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Color(0xFF0F0F0F),
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    indicator.icon,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${indicator.name} Configuration',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        indicator.description,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(
                    LucideIcons.x,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),

          // Configuration content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: _buildIndicatorConfiguration(indicator),
            ),
          ),

          // Save button
          Padding(
            padding: const EdgeInsets.all(24),
            child: GradientButton(
              text: 'Save Configuration',
              onPressed: () => Navigator.pop(context),
              icon: LucideIcons.check,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIndicatorConfiguration(TradingIndicator indicator) {
    switch (indicator.id) {
      case 'rsi':
        return _buildRSIConfiguration();
      case 'macd':
        return _buildMACDConfiguration();
      case 'bollinger':
        return _buildBollingerConfiguration();
      case 'ema':
        return _buildEMAConfiguration();
      case 'sma':
        return _buildSMAConfiguration();
      case 'stochastic':
        return _buildStochasticConfiguration();
      case 'volume':
        return _buildVolumeConfiguration();
      case 'support_resistance':
        return _buildSupportResistanceConfiguration();
      default:
        return _buildDefaultConfiguration(indicator);
    }
  }

  Widget _buildRSIConfiguration() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildConfigSection(
          'RSI Parameters',
          'Configure Relative Strength Index settings',
          [
            _buildSliderConfig('Period', 14, 5, 50, (value) {}),
            _buildSliderConfig('Overbought Level', 70, 60, 90, (value) {}),
            _buildSliderConfig('Oversold Level', 30, 10, 40, (value) {}),
          ],
        ),
        const SizedBox(height: 24),
        _buildConfigSection(
          'Trading Signals',
          'Configure when to generate buy/sell signals',
          [
            _buildSwitchConfig('Buy on Oversold', true, (value) {}),
            _buildSwitchConfig('Sell on Overbought', true, (value) {}),
            _buildSwitchConfig('Divergence Detection', false, (value) {}),
          ],
        ),
      ],
    );
  }

  Widget _buildMACDConfiguration() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildConfigSection(
          'MACD Parameters',
          'Configure Moving Average Convergence Divergence',
          [
            _buildSliderConfig('Fast Period', 12, 5, 20, (value) {}),
            _buildSliderConfig('Slow Period', 26, 20, 50, (value) {}),
            _buildSliderConfig('Signal Period', 9, 5, 15, (value) {}),
          ],
        ),
        const SizedBox(height: 24),
        _buildConfigSection(
          'Signal Configuration',
          'Configure MACD trading signals',
          [
            _buildSwitchConfig('Signal Line Crossover', true, (value) {}),
            _buildSwitchConfig('Zero Line Crossover', false, (value) {}),
            _buildSwitchConfig('Histogram Analysis', true, (value) {}),
          ],
        ),
      ],
    );
  }

  Widget _buildBollingerConfiguration() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildConfigSection(
          'Bollinger Bands Parameters',
          'Configure volatility bands settings',
          [
            _buildSliderConfig('Period', 20, 10, 50, (value) {}),
            _buildSliderConfig('Standard Deviation', 2, 1, 3, (value) {}),
          ],
        ),
        const SizedBox(height: 24),
        _buildConfigSection(
          'Trading Strategy',
          'Configure band-based trading signals',
          [
            _buildSwitchConfig('Buy at Lower Band', true, (value) {}),
            _buildSwitchConfig('Sell at Upper Band', true, (value) {}),
            _buildSwitchConfig('Squeeze Detection', false, (value) {}),
          ],
        ),
      ],
    );
  }

  Widget _buildEMAConfiguration() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildConfigSection(
          'EMA Parameters',
          'Configure Exponential Moving Average',
          [
            _buildSliderConfig('Short Period', 12, 5, 30, (value) {}),
            _buildSliderConfig('Long Period', 26, 20, 100, (value) {}),
          ],
        ),
        const SizedBox(height: 24),
        _buildConfigSection(
          'Crossover Signals',
          'Configure EMA crossover strategy',
          [
            _buildSwitchConfig('Golden Cross (Buy)', true, (value) {}),
            _buildSwitchConfig('Death Cross (Sell)', true, (value) {}),
            _buildSwitchConfig('Price Above/Below EMA', false, (value) {}),
          ],
        ),
      ],
    );
  }

  Widget _buildSMAConfiguration() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildConfigSection(
          'SMA Parameters',
          'Configure Simple Moving Average',
          [
            _buildSliderConfig('Short Period', 20, 5, 50, (value) {}),
            _buildSliderConfig('Long Period', 50, 30, 200, (value) {}),
          ],
        ),
        const SizedBox(height: 24),
        _buildConfigSection('Trading Signals', 'Configure SMA-based signals', [
          _buildSwitchConfig('Price Crossover', true, (value) {}),
          _buildSwitchConfig('SMA Crossover', true, (value) {}),
          _buildSwitchConfig('Trend Confirmation', false, (value) {}),
        ]),
      ],
    );
  }

  Widget _buildStochasticConfiguration() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildConfigSection(
          'Stochastic Parameters',
          'Configure Stochastic Oscillator',
          [
            _buildSliderConfig('%K Period', 14, 5, 30, (value) {}),
            _buildSliderConfig('%D Period', 3, 1, 10, (value) {}),
            _buildSliderConfig('Smooth', 3, 1, 10, (value) {}),
          ],
        ),
        const SizedBox(height: 24),
        _buildConfigSection(
          'Signal Levels',
          'Configure overbought/oversold levels',
          [
            _buildSliderConfig('Overbought', 80, 70, 90, (value) {}),
            _buildSliderConfig('Oversold', 20, 10, 30, (value) {}),
          ],
        ),
      ],
    );
  }

  Widget _buildVolumeConfiguration() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildConfigSection(
          'Volume Analysis',
          'Configure volume-based indicators',
          [
            _buildSliderConfig('Volume MA Period', 20, 5, 50, (value) {}),
            _buildSliderConfig(
              'Volume Spike Threshold',
              150,
              100,
              300,
              (value) {},
            ),
          ],
        ),
        const SizedBox(height: 24),
        _buildConfigSection(
          'Volume Signals',
          'Configure volume-based trading signals',
          [
            _buildSwitchConfig('Volume Breakout', true, (value) {}),
            _buildSwitchConfig('Volume Confirmation', true, (value) {}),
            _buildSwitchConfig('On-Balance Volume', false, (value) {}),
          ],
        ),
      ],
    );
  }

  Widget _buildSupportResistanceConfiguration() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildConfigSection(
          'Level Detection',
          'Configure support and resistance detection',
          [
            _buildSliderConfig('Lookback Period', 20, 10, 50, (value) {}),
            _buildSliderConfig('Strength Threshold', 3, 2, 10, (value) {}),
          ],
        ),
        const SizedBox(height: 24),
        _buildConfigSection(
          'Trading Strategy',
          'Configure level-based trading',
          [
            _buildSwitchConfig('Buy at Support', true, (value) {}),
            _buildSwitchConfig('Sell at Resistance', true, (value) {}),
            _buildSwitchConfig('Breakout Trading', false, (value) {}),
          ],
        ),
      ],
    );
  }

  Widget _buildDefaultConfiguration(TradingIndicator indicator) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildConfigSection(
          'General Settings',
          'Basic configuration for ${indicator.name}',
          [
            _buildSwitchConfig('Enable Signal Generation', true, (value) {}),
            _buildSwitchConfig(
              'Use in Bot Strategy',
              indicator.isEnabled,
              (value) {},
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildConfigSection(
    String title,
    String subtitle,
    List<Widget> children,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          subtitle,
          style: TextStyle(
            fontSize: 14,
            color: Colors.white.withValues(alpha: 0.6),
          ),
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildSliderConfig(
    String label,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              Text(
                value.toInt().toString(),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: AppTheme.primaryColor,
              inactiveTrackColor: Colors.white.withValues(alpha: 0.2),
              thumbColor: AppTheme.primaryColor,
              overlayColor: AppTheme.primaryColor.withValues(alpha: 0.2),
              trackHeight: 4,
            ),
            child: Slider(
              value: value,
              min: min,
              max: max,
              divisions: (max - min).toInt(),
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchConfig(
    String label,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.primaryColor,
            activeTrackColor: AppTheme.primaryColor.withValues(alpha: 0.3),
            inactiveThumbColor: Colors.white.withValues(alpha: 0.6),
            inactiveTrackColor: Colors.white.withValues(alpha: 0.1),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
