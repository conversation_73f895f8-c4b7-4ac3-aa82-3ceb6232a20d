import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../../services/user_preferences_service.dart';
import '../../theme/app_theme.dart';
import '../../utils/responsive_helper.dart';

class NotificationSettingsPage extends StatefulWidget {
  const NotificationSettingsPage({super.key});

  @override
  State<NotificationSettingsPage> createState() => _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends State<NotificationSettingsPage> {
  final UserPreferencesService _preferencesService = UserPreferencesService();
  UserPreferences? _preferences;
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadPreferences();
  }

  Future<void> _loadPreferences() async {
    try {
      final prefs = await _preferencesService.getUserPreferences();
      if (mounted) {
        setState(() {
          _preferences = prefs;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading preferences: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updatePreference(String key, bool value) async {
    if (_preferences == null) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final success = await _preferencesService.updatePreference(key, value);
      
      if (success && mounted) {
        setState(() {
          switch (key) {
            case 'notifications_enabled':
              _preferences = _preferences!.copyWith(notificationsEnabled: value);
              break;
            case 'trading_notifications':
              _preferences = _preferences!.copyWith(tradingNotifications: value);
              break;
            case 'price_alerts':
              _preferences = _preferences!.copyWith(priceAlerts: value);
              break;
          }
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Preference updated'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        throw Exception('Failed to update preference');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error updating preference: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
       backgroundColor: ResponsiveHelper.isDesktop(context) ? Colors.transparent : Theme.of(context).scaffoldBackgroundColor,
      appBar: ResponsiveHelper.isMobile(context) ? AppBar(
        title: const Text('Notification Settings'),
        centerTitle: false,
        elevation: 0,
        backgroundColor: Colors.transparent,
      ) : null,
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _preferences == null
              ? _buildErrorState()
              : _buildContent(),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            LucideIcons.triangle,
            size: 64,
            color: Colors.orange,
          ),
          const SizedBox(height: 16),
          const Text(
            'Unable to load preferences',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          const Text(
            'Please try again later',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadPreferences,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: ResponsiveHelper.getPagePadding(context),
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: ResponsiveHelper.getMaxContentWidth(context),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (ResponsiveHelper.isDesktop(context)) ...[
                Text(
                  'Notification Settings',
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getFontSize(context, 28),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Manage your notification preferences',
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getFontSize(context, 16),
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 32),
              ],

              _buildNotificationCard(
                icon: LucideIcons.bell,
                title: 'All Notifications',
                subtitle: 'Enable or disable all notifications',
                value: _preferences!.notificationsEnabled,
                onChanged: (value) => _updatePreference('notifications_enabled', value),
              ),
              
              const SizedBox(height: 16),
              
              _buildNotificationCard(
                icon: LucideIcons.trendingUp,
                title: 'Trading Notifications',
                subtitle: 'Get notified about trading opportunities',
                value: _preferences!.tradingNotifications,
                onChanged: _preferences!.notificationsEnabled 
                  ? (value) => _updatePreference('trading_notifications', value)
                  : null,
              ),
              
              const SizedBox(height: 16),
              
              _buildNotificationCard(
                icon: LucideIcons.triangle,
                title: 'Price Alerts',
                subtitle: 'Receive alerts when prices change significantly',
                value: _preferences!.priceAlerts,
                onChanged: _preferences!.notificationsEnabled 
                  ? (value) => _updatePreference('price_alerts', value)
                  : null,
              ),

              const SizedBox(height: 32),

              _buildInfoCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool)? onChanged,
  }) {
    final isEnabled = onChanged != null;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isEnabled ? AppTheme.primaryColor.withValues(alpha: 0.1) : Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: isEnabled ? AppTheme.primaryColor : Colors.grey,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isEnabled ? null : Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (_isSaving)
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            else
              Switch(
                value: value,
                onChanged: onChanged,
                activeColor: AppTheme.primaryColor,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      elevation: 1,
      color: Colors.blue.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const Icon(
              LucideIcons.info,
              color: Colors.blue,
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'About Notifications',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Notifications help you stay updated with market changes and trading opportunities. You can customize which types of notifications you receive.',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
