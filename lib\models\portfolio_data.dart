class PortfolioData {
  final double totalValue;
  final double totalChange;
  final double totalChangePercent;
  final List<TokenHolding> holdings;

  PortfolioData({
    required this.totalValue,
    required this.totalChange,
    required this.totalChangePercent,
    required this.holdings,
  });

  factory PortfolioData.empty() {
    return PortfolioData(
      totalValue: 0.0,
      totalChange: 0.0,
      totalChangePercent: 0.0,
      holdings: [],
    );
  }
}

class TokenHolding {
  final String symbol;
  final String name;
  final double amount;
  final double value;
  final double change;
  final double changePercent;
  final String? iconUrl;
  final String? imageUrl;
  final double balance;

  TokenHolding({
    required this.symbol,
    required this.name,
    required this.amount,
    required this.value,
    required this.change,
    required this.changePercent,
    this.iconUrl,
    this.imageUrl,
    required this.balance,
  });

  factory TokenHolding.fromJson(Map<String, dynamic> json) {
    return TokenHolding(
      symbol: json['symbol'] ?? '',
      name: json['name'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      value: (json['value'] ?? 0).toDouble(),
      change: (json['change'] ?? 0).toDouble(),
      changePercent: (json['change_percent'] ?? 0).toDouble(),
      iconUrl: json['icon_url'],
      imageUrl: json['image_url'],
      balance: (json['balance'] ?? 0).toDouble(),
    );
  }
}
