import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../theme/app_theme.dart';
import '../pages/recommendation_page.dart';

class SwipeableRecommendationCard extends StatelessWidget {
  final Recommendation recommendation;
  final double swipeProgress;
  final bool isSwipingRight;

  const SwipeableRecommendationCard({
    super.key,
    required this.recommendation,
    this.swipeProgress = 0.0,
    this.isSwipingRight = false,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Base card
        TradingRecommendationCard(recommendation: recommendation),

        // Gradient overlay based on swipe direction
        if (swipeProgress > 0.1)
          Positioned.fill(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  colors: isSwipingRight
                      ? [
                          AppTheme.primaryColor.withValues(alpha: 0.0),
                          AppTheme.primaryColor.withValues(
                            alpha: swipeProgress * 0.8,
                          ),
                        ]
                      : [
                          Colors.red.withValues(alpha: 0.0),
                          Colors.red.withValues(alpha: swipeProgress * 0.8),
                        ],
                  begin: isSwipingRight
                      ? Alignment.centerLeft
                      : Alignment.centerRight,
                  end: isSwipingRight
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
                ),
              ),
            ),
          ),

        // Icon overlay
        if (swipeProgress > 0.2)
          Positioned.fill(
            child: Center(
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  isSwipingRight ? LucideIcons.trendingUp : LucideIcons.x,
                  color: isSwipingRight ? AppTheme.primaryColor : Colors.red,
                  size: 40,
                ),
              ),
            ),
          ),
      ],
    );
  }
}

class TradingRecommendationCard extends StatelessWidget {
  final Recommendation recommendation;

  const TradingRecommendationCard({super.key, required this.recommendation});

  @override
  Widget build(BuildContext context) {
    final coin = recommendation.coin;
    final isPositive =
        coin.priceChangePercentage24h != null &&
        coin.priceChangePercentage24h! >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.03),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Coin details header (similar to coin page)
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                // Coin icon
                coin.image != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: Image.network(
                          coin.image!,
                          width: 40,
                          height: 40,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              Container(
                                width: 40,
                                height: 40,
                                decoration: BoxDecoration(
                                  color: Colors.grey[300],
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Icon(
                                  LucideIcons.coins,
                                  color: Colors.grey[600],
                                  size: 20,
                                ),
                              ),
                        ),
                      )
                    : Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Center(
                          child: Text(
                            coin.symbol.substring(0, 1).toUpperCase(),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryColor,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                const SizedBox(width: 12),

                // Coin name and symbol
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        coin.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          color: Colors.white,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Row(
                        children: [
                          Text(
                            coin.symbol.toUpperCase(),
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (coin.marketCapRank != null) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey[200],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                '#${coin.marketCapRank}',
                                style: TextStyle(
                                  color: Colors.grey[700],
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),

                // Price and change
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      coin.formattedPrice,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    if (coin.priceChangePercentage24h != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: changeColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              isPositive
                                  ? LucideIcons.trendingUp
                                  : LucideIcons.trendingDown,
                              color: changeColor,
                              size: 12,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              coin.formattedPriceChange,
                              style: TextStyle(
                                color: changeColor,
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),

          // Trading signal header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Icon(
                  LucideIcons.clock,
                  color: Colors.white.withValues(alpha: 0.7),
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  recommendation.timeAgo,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 14,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: recommendation.direction == 'Long'
                        ? Colors.green
                        : Colors.red,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        recommendation.direction == 'Long'
                            ? LucideIcons.trendingUp
                            : LucideIcons.trendingDown,
                        color: Colors.white,
                        size: 14,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        recommendation.direction,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Description
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              recommendation.description,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.9),
                fontSize: 15,
                height: 1.4,
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Chart placeholder with price info
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            height: 200,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: Stack(
              children: [
                // Mock chart
                Positioned.fill(
                  child: CustomPaint(
                    painter: MockChartPainter(
                      data: recommendation.chartData,
                      isPositive: recommendation.direction == 'Long',
                    ),
                  ),
                ),

                // Price labels
                Positioned(
                  top: 12,
                  right: 12,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        coin.formattedPrice,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: changeColor,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          '${recommendation.takeProfit > 0 ? '+' : ''}${recommendation.takeProfit.toStringAsFixed(1)}%',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Support/Resistance lines
                Positioned(
                  bottom: 40,
                  left: 12,
                  right: 12,
                  child: Row(
                    children: [
                      Container(width: 8, height: 2, color: Colors.green),
                      const SizedBox(width: 8),
                      Text(
                        'TP',
                        style: TextStyle(color: Colors.grey[600], fontSize: 10),
                      ),
                      const Spacer(),
                      Text(
                        '${recommendation.stopLossPercent.toStringAsFixed(0)}%',
                        style: const TextStyle(
                          color: Colors.red,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'SL',
                        style: TextStyle(color: Colors.grey[600], fontSize: 10),
                      ),
                      const SizedBox(width: 8),
                      Container(width: 8, height: 2, color: Colors.red),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Trading stats
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.green.withValues(alpha: 0.3),
                      width: 0.5,
                    ),
                  ),
                  child: Text(
                    'T 1.00',
                    style: const TextStyle(
                      color: Colors.green,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.15),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.red.withValues(alpha: 0.3),
                      width: 0.5,
                    ),
                  ),
                  child: const Icon(LucideIcons.x, color: Colors.red, size: 12),
                ),
                const Spacer(),
                Text(
                  'TP ${recommendation.takeProfit > 0 ? '+' : ''}${recommendation.takeProfit.toStringAsFixed(0)}%',
                  style: const TextStyle(
                    color: Colors.green,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  '${recommendation.stopLossPercent.toStringAsFixed(0)}% SL',
                  style: const TextStyle(
                    color: Colors.red,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }
}

class MockChartPainter extends CustomPainter {
  final List<double> data;
  final bool isPositive;

  MockChartPainter({required this.data, required this.isPositive});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = isPositive ? Colors.green : Colors.red
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final path = Path();

    for (int i = 0; i < data.length; i++) {
      final x = (i / (data.length - 1)) * size.width;
      final y = size.height - (data[i] * size.height);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
