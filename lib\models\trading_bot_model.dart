class Trade {
  final String id;
  final String type; // 'BUY' or 'SELL'
  final double price;
  final double amount;
  final double profit;
  final DateTime timestamp;
  final String status; // 'SUCCESS', 'PENDING', 'FAILED'

  Trade({
    required this.id,
    required this.type,
    required this.price,
    required this.amount,
    required this.profit,
    required this.timestamp,
    required this.status,
  });
}

class TradingBot {
  final String id;
  final String name;
  final String tokenSymbol;
  final String tokenImage;
  double currentPrice;
  double priceChange24h;
  bool isActive;
  bool isArchived;
  final String strategy;
  final double solPerTrade;
  int totalTrades;
  double winRate;
  double totalEarned;
  List<Trade> trades;

  TradingBot({
    required this.id,
    required this.name,
    required this.tokenSymbol,
    required this.tokenImage,
    required this.currentPrice,
    required this.priceChange24h,
    required this.isActive,
    this.isArchived = false,
    required this.strategy,
    required this.solPerTrade,
    required this.totalTrades,
    required this.winRate,
    required this.totalEarned,
    required this.trades,
  });
}
