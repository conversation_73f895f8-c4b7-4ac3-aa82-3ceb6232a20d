version: '3.8'

services:
  trading-bot:
    build: .
    container_name: trading-bot-server
    restart: unless-stopped
    environment:
      # Database configuration
      - DATABASE_URL=${DATABASE_URL}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      
      # Trading configuration
      - TRADING_INTERVAL_SECONDS=${TRADING_INTERVAL_SECONDS:-60}
      - MAX_CONCURRENT_BOTS=${MAX_CONCURRENT_BOTS:-100}
      - DEFAULT_TRADING_FEE=${DEFAULT_TRADING_FEE:-0.001}
      
      # Risk management
      - MAX_POSITION_SIZE=${MAX_POSITION_SIZE:-10000}
      - MAX_DAILY_LOSS=${MAX_DAILY_LOSS:-1000}
      
      # Logging
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FILE=${LOG_FILE}
      
      # Environment
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - DEBUG_MODE=${DEBUG_MODE:-false}
      - DRY_RUN=${DRY_RUN:-false}
      
      # Monitoring
      - ENABLE_METRICS=${ENABLE_METRICS:-true}
      - METRICS_PORT=8000
      
      # Notifications
      - WEBHOOK_URL=${WEBHOOK_URL}
      - DISCORD_WEBHOOK=${DISCORD_WEBHOOK}
      
      # Redis (optional)
      - REDIS_URL=${REDIS_URL}
    
    ports:
      - "8000:8000"  # Metrics port
    
    volumes:
      - ./logs:/app/logs  # Log directory
      - ./data:/app/data  # Data directory for any local files
    
    networks:
      - trading-network
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Redis for caching
  redis:
    image: redis:7-alpine
    container_name: trading-bot-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - trading-network
    ports:
      - "6379:6379"

  # Optional: Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: trading-bot-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - trading-network

  # Optional: Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: trading-bot-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - trading-network

networks:
  trading-network:
    driver: bridge

volumes:
  redis_data:
  prometheus_data:
  grafana_data:
