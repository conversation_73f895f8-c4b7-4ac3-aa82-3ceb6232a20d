import 'package:flutter/material.dart';

class SwipeableCardStack extends StatefulWidget {
  final List<Widget> cards;
  final Function(int index, SwipeDirection direction) onSwipe;
  final Function(double progress, bool isRight)? onSwipeProgress;
  final Function(bool isDragging)? onDragStateChange;
  final double cardHeight;
  final double cardWidth;
  final int visibleCards;

  const SwipeableCardStack({
    super.key,
    required this.cards,
    required this.onSwipe,
    this.onSwipeProgress,
    this.onDragStateChange,
    this.cardHeight = 600,
    this.cardWidth = double.infinity,
    this.visibleCards = 3,
  });

  @override
  State<SwipeableCardStack> createState() => SwipeableCardStackState();
}

class SwipeableCardStackState extends State<SwipeableCardStack>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  int _currentIndex = 0;
  Offset _dragOffset = Offset.zero;
  bool _isDragging = false;
  double _rotation = 0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  void _onPanStart(DragStartDetails details) {
    setState(() {
      _isDragging = true;
    });
    _scaleController.forward();
    widget.onDragStateChange?.call(true);
  }

  void _onPanUpdate(DragUpdateDetails details) {
    setState(() {
      // Only allow horizontal movement for tilt effect
      _dragOffset = Offset(_dragOffset.dx + details.delta.dx, 0);
      // Increase rotation sensitivity for more dramatic tilt
      _rotation = (_dragOffset.dx / 200) * 0.5;
      // Clamp rotation to reasonable limits
      _rotation = _rotation.clamp(-0.5, 0.5);

      // Calculate swipe progress and notify parent
      final progress = (_dragOffset.dx.abs() / 150).clamp(0.0, 1.0);
      final isRight = _dragOffset.dx > 0;
      widget.onSwipeProgress?.call(progress, isRight);
    });
  }

  void _onPanEnd(DragEndDetails details) {
    _scaleController.reverse();
    widget.onDragStateChange?.call(false);

    final threshold =
        MediaQuery.of(context).size.width *
        0.2; // Lower threshold for easier swipe
    final velocity = details.velocity.pixelsPerSecond.dx;

    SwipeDirection? direction;

    if (_dragOffset.dx > threshold || velocity > 500) {
      direction = SwipeDirection.right;
    } else if (_dragOffset.dx < -threshold || velocity < -500) {
      direction = SwipeDirection.left;
    }

    if (direction != null) {
      _animateCardOut(direction);
    } else {
      _resetCard();
    }
  }

  void _animateCardOut(SwipeDirection direction) {
    // Set the final rotation based on direction
    final finalRotation = direction == SwipeDirection.right ? 0.5 : -0.5;
    _rotation = finalRotation;

    _animationController.forward().then((_) {
      widget.onSwipe(_currentIndex, direction);
      setState(() {
        _currentIndex++;
        _dragOffset = Offset.zero;
        _rotation = 0;
        _isDragging = false;
      });
      _animationController.reset();
      // Reset swipe progress and drag state
      widget.onSwipeProgress?.call(0.0, false);
      widget.onDragStateChange?.call(false);
    });
  }

  void _resetCard() {
    setState(() {
      _dragOffset = Offset.zero;
      _rotation = 0;
      _isDragging = false;
    });
    widget.onSwipeProgress?.call(0.0, false);
  }

  void swipeLeft() {
    setState(() {
      _dragOffset = Offset(-100, 0); // Set initial position for animation
      _rotation = -0.3; // Set initial rotation
    });
    _animateCardOut(SwipeDirection.left);
  }

  void swipeRight() {
    setState(() {
      _dragOffset = Offset(100, 0); // Set initial position for animation
      _rotation = 0.3; // Set initial rotation
    });
    _animateCardOut(SwipeDirection.right);
  }

  @override
  Widget build(BuildContext context) {
    if (_currentIndex >= widget.cards.length) {
      return const Center(
        child: Text(
          'No more cards',
          style: TextStyle(color: Colors.white, fontSize: 18),
        ),
      );
    }

    return SizedBox(
      height: widget.cardHeight,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Single card only
          if (_currentIndex < widget.cards.length)
            AnimatedBuilder(
              animation: _scaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  child: Transform.translate(
                    offset: _isDragging
                        ? Offset(
                            _dragOffset.dx,
                            0,
                          ) // Only horizontal movement during drag
                        : Offset(
                            _animation.value *
                                (_rotation > 0
                                    ? MediaQuery.of(context).size.width + 100
                                    : -MediaQuery.of(context).size.width - 100),
                            _animation.value *
                                _rotation *
                                100, // Slight vertical movement
                          ),
                    child: Transform.rotate(
                      angle: _isDragging
                          ? _rotation
                          : _animation.value * _rotation,
                      child: GestureDetector(
                        onPanStart: _onPanStart,
                        onPanUpdate: _onPanUpdate,
                        onPanEnd: _onPanEnd,
                        child: widget.cards[_currentIndex],
                      ),
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }
}

enum SwipeDirection { left, right }
