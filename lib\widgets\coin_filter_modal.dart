import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../theme/app_theme.dart';

class CoinFilterModal extends StatefulWidget {
  final Map<String, dynamic> currentFilters;
  final Function(Map<String, dynamic>) onFiltersChanged;

  const CoinFilterModal({
    super.key,
    required this.currentFilters,
    required this.onFiltersChanged,
  });

  @override
  State<CoinFilterModal> createState() => _CoinFilterModalState();
}

class _CoinFilterModalState extends State<CoinFilterModal> {
  late Map<String, dynamic> _filters;

  // Filter options
  final List<String> _platforms = [
    'All Platforms',
    'Solana',
    'PumpFun',
    'Raydium',
    'Jupiter',
    'Orca',
  ];

  final List<String> _marketCapRanges = [
    'All Market Caps',
    'Under \$1M',
    '\$1M - \$10M',
    '\$10M - \$100M',
    '\$100M - \$1B',
    'Over \$1B',
  ];

  final List<String> _liquidityRanges = [
    'All Liquidity',
    'Under \$10K',
    '\$10K - \$100K',
    '\$100K - \$1M',
    '\$1M - \$10M',
    'Over \$10M',
  ];

  final List<String> _timePeriods = ['1H', '24H', '7D', '30D', '90D', '1Y'];

  final List<String> _sortOptions = [
    'Market Cap',
    'Price',
    'Volume',
    '24h Change',
    'Liquidity',
    'Age',
  ];

  @override
  void initState() {
    super.initState();
    _filters = Map<String, dynamic>.from(widget.currentFilters);

    // Initialize default values if not set
    _filters.putIfAbsent('platform', () => 'All Platforms');
    _filters.putIfAbsent('marketCap', () => 'All Market Caps');
    _filters.putIfAbsent('liquidity', () => 'All Liquidity');
    _filters.putIfAbsent('timePeriod', () => '24H');
    _filters.putIfAbsent('sortBy', () => 'Market Cap');
    _filters.putIfAbsent('sortOrder', () => 'desc');
    _filters.putIfAbsent('minPrice', () => 0.0);
    _filters.putIfAbsent('maxPrice', () => 1000000.0);
    _filters.putIfAbsent('showOnlyVerified', () => false);
    _filters.putIfAbsent('showOnlyTrending', () => false);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.transparent
            : Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white24
                  : Colors.grey.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    LucideIcons.settings,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Filter Coins',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.bodyLarge?.color,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(
                    LucideIcons.x,
                    color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),

          // Filter content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Platform filter
                  _buildFilterSection(
                    'Platform',
                    LucideIcons.layers,
                    _buildDropdownFilter('platform', _platforms),
                  ),

                  const SizedBox(height: 24),

                  // Market Cap filter
                  _buildFilterSection(
                    'Market Cap',
                    LucideIcons.dollarSign,
                    _buildDropdownFilter('marketCap', _marketCapRanges),
                  ),

                  const SizedBox(height: 24),

                  // Liquidity filter
                  _buildFilterSection(
                    'Liquidity',
                    LucideIcons.droplets,
                    _buildDropdownFilter('liquidity', _liquidityRanges),
                  ),

                  const SizedBox(height: 24),

                  // Time Period filter
                  _buildFilterSection(
                    'Time Period',
                    LucideIcons.clock,
                    _buildChipFilter('timePeriod', _timePeriods),
                  ),

                  const SizedBox(height: 24),

                  // Sort options
                  _buildFilterSection(
                    'Sort By',
                    LucideIcons.arrowUpDown,
                    Column(
                      children: [
                        _buildDropdownFilter('sortBy', _sortOptions),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: _buildSortOrderButton(
                                'Highest First',
                                'desc',
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildSortOrderButton(
                                'Lowest First',
                                'asc',
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Price range filter
                  _buildFilterSection(
                    'Price Range',
                    LucideIcons.trendingUp,
                    _buildPriceRangeFilter(),
                  ),

                  const SizedBox(height: 24),

                  // Toggle filters
                  _buildFilterSection(
                    'Additional Filters',
                    LucideIcons.settings,
                    Column(
                      children: [
                        _buildToggleFilter(
                          'Show only verified coins',
                          'showOnlyVerified',
                          LucideIcons.shieldCheck,
                        ),
                        const SizedBox(height: 12),
                        _buildToggleFilter(
                          'Show only trending coins',
                          'showOnlyTrending',
                          LucideIcons.trendingUp,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),

          // Action buttons
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.black.withValues(alpha: 0.3)
                  : Colors.grey.withValues(alpha: 0.1),
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.1)
                      : Colors.grey.withValues(alpha: 0.3),
                ),
              ),
            ),
            child: SafeArea(
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _resetFilters,
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.8),
                        side: BorderSide(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white30
                              : Colors.grey.withValues(alpha: 0.5),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text('Reset All'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: _applyFilters,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Apply Filters',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(String title, IconData icon, Widget content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.8),
              size: 18,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        content,
      ],
    );
  }

  Widget _buildDropdownFilter(String key, List<String> options) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.white.withValues(alpha: 0.05)
            : Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white.withValues(alpha: 0.1)
              : Colors.grey.withValues(alpha: 0.3),
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _filters[key],
          isExpanded: true,
          dropdownColor: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF2A2A2A)
              : Colors.white,
          style: TextStyle(
            color: Theme.of(context).textTheme.bodyLarge?.color,
          ),
          items: options.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(value),
            );
          }).toList(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                _filters[key] = newValue;
              });
            }
          },
        ),
      ),
    );
  }

  Widget _buildChipFilter(String key, List<String> options) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: options.map((option) {
        final isSelected = _filters[key] == option;
        return GestureDetector(
          onTap: () {
            setState(() {
              _filters[key] = option;
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppTheme.primaryColor
                  : Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.05)
                      : Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected
                    ? AppTheme.primaryColor
                    : Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withValues(alpha: 0.1)
                        : Colors.grey.withValues(alpha: 0.3),
              ),
            ),
            child: Text(
              option,
              style: TextStyle(
                color: isSelected
                    ? Colors.white
                    : Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.8),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSortOrderButton(String title, String order) {
    final isSelected = _filters['sortOrder'] == order;
    return GestureDetector(
      onTap: () {
        setState(() {
          _filters['sortOrder'] = order;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryColor.withValues(alpha: 0.2)
              : Theme.of(context).brightness == Brightness.dark
                  ? Colors.white.withValues(alpha: 0.05)
                  : Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? AppTheme.primaryColor
                : Theme.of(context).brightness == Brightness.dark
                    ? Colors.white.withValues(alpha: 0.1)
                    : Colors.grey.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              order == 'desc' ? LucideIcons.arrowDown : LucideIcons.arrowUp,
              color: isSelected
                  ? AppTheme.primaryColor
                  : Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.8),
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                color: isSelected
                    ? AppTheme.primaryColor
                    : Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.8),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceRangeFilter() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Min Price (\$)',
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.8),
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 4),
                  TextFormField(
                    initialValue: _filters['minPrice'].toString(),
                    keyboardType: TextInputType.number,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodyLarge?.color,
                    ),
                    decoration: InputDecoration(
                      filled: true,
                      fillColor: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.05)
                          : Colors.grey.withValues(alpha: 0.1),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.1)
                              : Colors.grey.withValues(alpha: 0.3),
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.1)
                              : Colors.grey.withValues(alpha: 0.3),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: AppTheme.primaryColor),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    onChanged: (value) {
                      final price = double.tryParse(value) ?? 0.0;
                      setState(() {
                        _filters['minPrice'] = price;
                      });
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Max Price (\$)',
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.8),
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 4),
                  TextFormField(
                    initialValue: _filters['maxPrice'].toString(),
                    keyboardType: TextInputType.number,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodyLarge?.color,
                    ),
                    decoration: InputDecoration(
                      filled: true,
                      fillColor: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.05)
                          : Colors.grey.withValues(alpha: 0.1),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.1)
                              : Colors.grey.withValues(alpha: 0.3),
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withValues(alpha: 0.1)
                              : Colors.grey.withValues(alpha: 0.3),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: AppTheme.primaryColor),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    onChanged: (value) {
                      final price = double.tryParse(value) ?? 1000000.0;
                      setState(() {
                        _filters['maxPrice'] = price;
                      });
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildToggleFilter(String title, String key, IconData icon) {
    final isEnabled = _filters[key] ?? false;
    return GestureDetector(
      onTap: () {
        setState(() {
          _filters[key] = !isEnabled;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white.withValues(alpha: 0.05)
              : Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isEnabled
                ? AppTheme.primaryColor
                : Theme.of(context).brightness == Brightness.dark
                    ? Colors.white.withValues(alpha: 0.1)
                    : Colors.grey.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isEnabled
                  ? AppTheme.primaryColor
                  : Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.8),
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  color: isEnabled
                      ? AppTheme.primaryColor
                      : Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.8),
                  fontWeight: isEnabled ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
            Switch(
              value: isEnabled,
              onChanged: (value) {
                setState(() {
                  _filters[key] = value;
                });
              },
              activeColor: AppTheme.primaryColor,
              activeTrackColor: AppTheme.primaryColor.withValues(alpha: 0.3),
            ),
          ],
        ),
      ),
    );
  }

  void _resetFilters() {
    setState(() {
      _filters = {
        'platform': 'All Platforms',
        'marketCap': 'All Market Caps',
        'liquidity': 'All Liquidity',
        'timePeriod': '24H',
        'sortBy': 'Market Cap',
        'sortOrder': 'desc',
        'minPrice': 0.0,
        'maxPrice': 1000000.0,
        'showOnlyVerified': false,
        'showOnlyTrending': false,
      };
    });
  }

  void _applyFilters() {
    widget.onFiltersChanged(_filters);
    Navigator.pop(context);
  }
}
