enum TransactionType {
  send,
  receive,
  swap,
  stake,
  unstake,
}

enum TransactionStatus {
  pending,
  confirmed,
  failed,
}

class Transaction {
  final String id;
  final TransactionType type;
  final TransactionStatus status;
  final double amount;
  final String symbol;
  final String? toAddress;
  final String? fromAddress;
  final DateTime timestamp;
  final String? hash;
  final double? fee;
  final String token;

  Transaction({
    required this.id,
    required this.type,
    required this.status,
    required this.amount,
    required this.symbol,
    this.toAddress,
    this.fromAddress,
    required this.timestamp,
    this.hash,
    this.fee,
    required this.token,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'],
      type: TransactionType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => TransactionType.send,
      ),
      status: TransactionStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => TransactionStatus.pending,
      ),
      amount: (json['amount'] ?? 0).toDouble(),
      symbol: json['symbol'] ?? '',
      toAddress: json['to_address'],
      fromAddress: json['from_address'],
      timestamp: DateTime.parse(json['timestamp']),
      hash: json['hash'],
      fee: json['fee']?.toDouble(),
      token: json['token'] ?? json['symbol'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'amount': amount,
      'symbol': symbol,
      'to_address': toAddress,
      'from_address': fromAddress,
      'timestamp': timestamp.toIso8601String(),
      'hash': hash,
      'fee': fee,
      'token': token,
    };
  }
}
