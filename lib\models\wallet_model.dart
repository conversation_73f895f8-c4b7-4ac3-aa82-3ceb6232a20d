enum WalletType {
  paper,
  solana,
  external, // For Phantom, Solflare, etc.
}

class WalletModel {
  final String id;
  final String name;
  final WalletType type;
  final String? address;
  final String? privateKey; // Only for generated wallets, encrypted in real app
  final double balance;
  final DateTime createdAt;
  final bool isActive;

  const WalletModel({
    required this.id,
    required this.name,
    required this.type,
    this.address,
    this.privateKey,
    required this.balance,
    required this.createdAt,
    this.isActive = false,
  });

  WalletModel copyWith({
    String? id,
    String? name,
    WalletType? type,
    String? address,
    String? privateKey,
    double? balance,
    DateTime? createdAt,
    bool? isActive,
  }) {
    return WalletModel(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      address: address ?? this.address,
      privateKey: privateKey ?? this.privateKey,
      balance: balance ?? this.balance,
      createdAt: createdAt ?? this.createdAt,
      isActive: isActive ?? this.isActive,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'address': address,
      'privateKey': privateKey,
      'balance': balance,
      'createdAt': createdAt.toIso8601String(),
      'isActive': isActive,
    };
  }

  factory WalletModel.fromJson(Map<String, dynamic> json) {
    return WalletModel(
      id: json['id'],
      name: json['name'],
      type: WalletType.values.firstWhere((e) => e.name == json['type']),
      address: json['address'],
      privateKey: json['privateKey'],
      balance: json['balance']?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(json['createdAt']),
      isActive: json['isActive'] ?? false,
    );
  }

  String get displayAddress {
    if (address == null || address!.length <= 8) return address ?? '';
    return '${address!.substring(0, 4)}...${address!.substring(address!.length - 4)}';
  }

  String get typeDisplayName {
    switch (type) {
      case WalletType.paper:
        return 'Paper Wallet';
      case WalletType.solana:
        return 'Solana Wallet';
      case WalletType.external:
        return 'External Wallet';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WalletModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class Transaction {
  final String id;
  final String walletId;
  final TransactionType type;
  final double amount;
  final String? fromAddress;
  final String? toAddress;
  final DateTime timestamp;
  final String? hash;
  final TransactionStatus status;
  final String? token; // SOL, USDC, etc.

  const Transaction({
    required this.id,
    required this.walletId,
    required this.type,
    required this.amount,
    this.fromAddress,
    this.toAddress,
    required this.timestamp,
    this.hash,
    required this.status,
    this.token = 'SOL',
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'walletId': walletId,
      'type': type.name,
      'amount': amount,
      'fromAddress': fromAddress,
      'toAddress': toAddress,
      'timestamp': timestamp.toIso8601String(),
      'hash': hash,
      'status': status.name,
      'token': token,
    };
  }

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'],
      walletId: json['walletId'],
      type: TransactionType.values.firstWhere((e) => e.name == json['type']),
      amount: json['amount']?.toDouble() ?? 0.0,
      fromAddress: json['fromAddress'],
      toAddress: json['toAddress'],
      timestamp: DateTime.parse(json['timestamp']),
      hash: json['hash'],
      status: TransactionStatus.values.firstWhere(
        (e) => e.name == json['status'],
      ),
      token: json['token'] ?? 'SOL',
    );
  }
}

enum TransactionType { send, receive, swap, stake, unstake }

enum TransactionStatus { pending, confirmed, failed }

class PortfolioData {
  final double totalValue;
  final double change24h;
  final double changePercent24h;
  final List<TokenHolding> holdings;
  final List<PortfolioHistoryPoint> history;

  const PortfolioData({
    required this.totalValue,
    required this.change24h,
    required this.changePercent24h,
    required this.holdings,
    required this.history,
  });
}

class TokenHolding {
  final String symbol;
  final String name;
  final double balance;
  final double price;
  final double value;
  final double change24h;
  final String? imageUrl;

  const TokenHolding({
    required this.symbol,
    required this.name,
    required this.balance,
    required this.price,
    required this.value,
    required this.change24h,
    this.imageUrl,
  });

  double get changePercent24h => price > 0 ? (change24h / price) * 100 : 0;
}

class PortfolioHistoryPoint {
  final DateTime timestamp;
  final double value;

  const PortfolioHistoryPoint({required this.timestamp, required this.value});
}
