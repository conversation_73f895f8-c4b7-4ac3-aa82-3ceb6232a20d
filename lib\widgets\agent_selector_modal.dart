import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../theme/app_theme.dart';

class Agent {
  final String id;
  final String name;
  final String avatarUrl;
  final String description;
  final double winRate;
  final int totalTrades;
  final bool isVerified;

  Agent({
    required this.id,
    required this.name,
    required this.avatarUrl,
    required this.description,
    required this.winRate,
    required this.totalTrades,
    this.isVerified = false,
  });
}

class AgentSelectorModal extends StatefulWidget {
  final String currentAgentId;
  final Function(Agent) onAgentSelected;

  const AgentSelectorModal({
    super.key,
    required this.currentAgentId,
    required this.onAgentSelected,
  });

  @override
  State<AgentSelectorModal> createState() => _AgentSelectorModalState();
}

class _AgentSelectorModalState extends State<AgentSelectorModal> {
  final List<Agent> _agents = [
    Agent(
      id: 'donalt',
      name: '<PERSON>A<PERSON>',
      avatarUrl: 'assets/avatars/agent.png',
      description: 'Expert crypto trader with focus on technical analysis',
      winRate: 78.5,
      totalTrades: 1247,
      isVerified: true,
    ),
    Agent(
      id: 'cryptobanter',
      name: 'CryptoBanter',
      avatarUrl: 'assets/avatars/agent2.png',
      description: 'Altcoin specialist with strong fundamental analysis',
      winRate: 72.3,
      totalTrades: 892,
      isVerified: true,
    ),
    Agent(
      id: 'coinbureau',
      name: 'Coin Bureau',
      avatarUrl: 'assets/avatars/agent3.png',
      description: 'Educational content creator with market insights',
      winRate: 69.8,
      totalTrades: 634,
      isVerified: false,
    ),
    Agent(
      id: 'benjamin',
      name: 'Benjamin Cowen',
      avatarUrl: 'assets/avatars/agent4.png',
      description: 'Data-driven analysis with long-term perspective',
      winRate: 81.2,
      totalTrades: 456,
      isVerified: true,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Color(0xFF1A1A1A),
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[600],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                const Text(
                  'Select AI Agent',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(LucideIcons.x, color: Colors.white),
                ),
              ],
            ),
          ),

          // Agent list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemCount: _agents.length,
              itemBuilder: (context, index) {
                final agent = _agents[index];
                final isSelected = agent.id == widget.currentAgentId;

                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppTheme.primaryColor.withValues(alpha: 0.1)
                        : const Color(0xFF2A2A2A),
                    borderRadius: BorderRadius.circular(12),
                    border: isSelected
                        ? Border.all(color: AppTheme.primaryColor, width: 2)
                        : null,
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.all(16),
                    leading: Stack(
                      children: [
                        CircleAvatar(
                          radius: 24,
                          backgroundImage: AssetImage(agent.avatarUrl),
                        ),
                        if (agent.isVerified)
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: const BoxDecoration(
                                color: Colors.blue,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                LucideIcons.check,
                                size: 12,
                                color: Colors.white,
                              ),
                            ),
                          ),
                      ],
                    ),
                    title: Row(
                      children: [
                        Text(
                          agent.name,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (isSelected) ...[
                          const SizedBox(width: 8),
                          const Icon(
                            LucideIcons.check,
                            color: AppTheme.primaryColor,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 4),
                        Text(
                          agent.description,
                          style: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.green.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                '${agent.winRate.toStringAsFixed(1)}% Win Rate',
                                style: const TextStyle(
                                  color: Colors.green,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '${agent.totalTrades} trades',
                              style: TextStyle(
                                color: Colors.grey[500],
                                fontSize: 10,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    onTap: () {
                      widget.onAgentSelected(agent);
                      Navigator.pop(context);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
