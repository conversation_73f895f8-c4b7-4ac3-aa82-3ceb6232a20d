import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class TokenService {
  static final TokenService _instance = TokenService._internal();
  factory TokenService() => _instance;
  TokenService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  StreamSubscription<List<Map<String, dynamic>>>? _tokenStreamSubscription;
  final StreamController<List<Map<String, dynamic>>> _tokenStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();

  Stream<List<Map<String, dynamic>>> get tokenStream =>
      _tokenStreamController.stream;

  /// Get token data by coin IDs
  Future<List<Map<String, dynamic>>> getTokensByIds(
    List<String> coinIds,
  ) async {
    try {
      if (coinIds.isEmpty) return [];

      debugPrint('🪙 Fetching tokens for coin IDs: $coinIds');

      final response = await _supabase
          .from('tokens')
          .select('*')
          .inFilter('coin_id', coinIds);

      debugPrint('✅ Fetched ${response.length} tokens');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ Error fetching tokens: $e');
      return [];
    }
  }

  /// Get single token by coin ID
  Future<Map<String, dynamic>?> getTokenById(String coinId) async {
    try {
      debugPrint('🪙 Fetching token for coin ID: $coinId');

      final response = await _supabase
          .from('tokens')
          .select('*')
          .eq('coin_id', coinId)
          .maybeSingle();

      if (response != null) {
        debugPrint('✅ Fetched token: ${response['symbol']}');
      } else {
        debugPrint('⚠️ No token found for coin ID: $coinId');
      }

      return response;
    } catch (e) {
      debugPrint('❌ Error fetching token: $e');
      return null;
    }
  }

  /// Start real-time subscription for specific coin IDs
  void startRealTimeSubscription(List<String> coinIds) {
    if (coinIds.isEmpty) return;

    debugPrint('🔄 Starting real-time token subscription for: $coinIds');

    // Cancel existing subscription
    _tokenStreamSubscription?.cancel();

    try {
      _tokenStreamSubscription = _supabase
          .from('tokens')
          .stream(primaryKey: ['id'])
          .inFilter('coin_id', coinIds)
          .listen(
            (data) {
              debugPrint('📡 Received token updates: ${data.length} tokens');
              _tokenStreamController.add(data);
            },
            onError: (error) {
              debugPrint('❌ Token stream error: $error');
            },
          );
    } catch (e) {
      debugPrint('❌ Error starting token subscription: $e');
    }
  }

  /// Stop real-time subscription
  void stopRealTimeSubscription() {
    debugPrint('⏹️ Stopping token real-time subscription');
    _tokenStreamSubscription?.cancel();
    _tokenStreamSubscription = null;
  }

  /// Get current price for a token
  double getCurrentPrice(Map<String, dynamic> tokenData) {
    return (tokenData['price'] as num?)?.toDouble() ?? 0.0;
  }

  /// Get 24h price change for a token
  double getPriceChange24h(Map<String, dynamic> tokenData) {
    return (tokenData['price_change_h24'] as num?)?.toDouble() ?? 0.0;
  }

  /// Get market cap for a token
  double getMarketCap(Map<String, dynamic> tokenData) {
    return (tokenData['market_cap'] as num?)?.toDouble() ?? 0.0;
  }

  /// Get 24h volume for a token
  double getVolume24h(Map<String, dynamic> tokenData) {
    return (tokenData['volume_h24'] as num?)?.toDouble() ?? 0.0;
  }

  /// Get OHLCV data from extra_data
  List<List<dynamic>> getOHLCVData(Map<String, dynamic> tokenData) {
    try {
      final extraData = tokenData['extra_data'] as Map<String, dynamic>?;
      if (extraData == null) return [];

      final recentOhlcv = extraData['recent_ohlcv'] as List<dynamic>?;
      if (recentOhlcv == null) return [];

      return recentOhlcv.map((item) => item as List<dynamic>).toList();
    } catch (e) {
      debugPrint('❌ Error parsing OHLCV data: $e');
      return [];
    }
  }

  /// Get latest close price from OHLCV data
  double getLatestClosePrice(Map<String, dynamic> tokenData) {
    try {
      final ohlcvData = getOHLCVData(tokenData);
      if (ohlcvData.isEmpty) return getCurrentPrice(tokenData);

      // OHLCV format: [timestamp, open, high, low, close, volume]
      final latestCandle = ohlcvData.first;
      if (latestCandle.length >= 5) {
        return (latestCandle[4] as num?)?.toDouble() ??
            getCurrentPrice(tokenData);
      }

      return getCurrentPrice(tokenData);
    } catch (e) {
      debugPrint('❌ Error getting latest close price: $e');
      return getCurrentPrice(tokenData);
    }
  }

  /// Calculate price change percentage
  double calculatePriceChangePercentage(
    double currentPrice,
    double previousPrice,
  ) {
    if (previousPrice == 0) return 0.0;
    return ((currentPrice - previousPrice) / previousPrice) * 100;
  }

  /// Format price for display
  String formatPrice(double price) {
    if (price == 0) return '\$0.00';

    if (price >= 1) {
      return '\$${price.toStringAsFixed(2)}';
    } else if (price >= 0.01) {
      return '\$${price.toStringAsFixed(4)}';
    } else if (price >= 0.0001) {
      return '\$${price.toStringAsFixed(6)}';
    } else {
      return '\$${price.toStringAsExponential(2)}';
    }
  }

  /// Format market cap for display
  String formatMarketCap(double marketCap) {
    if (marketCap >= 1e9) {
      return '\$${(marketCap / 1e9).toStringAsFixed(2)}B';
    } else if (marketCap >= 1e6) {
      return '\$${(marketCap / 1e6).toStringAsFixed(2)}M';
    } else if (marketCap >= 1e3) {
      return '\$${(marketCap / 1e3).toStringAsFixed(2)}K';
    } else {
      return '\$${marketCap.toStringAsFixed(2)}';
    }
  }

  /// Format volume for display
  String formatVolume(double volume) {
    return formatMarketCap(volume); // Same formatting as market cap
  }

  /// Format percentage change for display
  String formatPercentageChange(double change) {
    final sign = change >= 0 ? '+' : '';
    return '$sign${change.toStringAsFixed(2)}%';
  }

  /// Check if token data is valid
  bool isValidTokenData(Map<String, dynamic>? tokenData) {
    if (tokenData == null) return false;

    final coinId = tokenData['coin_id'] as String?;
    final symbol = tokenData['symbol'] as String?;

    return coinId != null &&
        coinId.isNotEmpty &&
        symbol != null &&
        symbol.isNotEmpty;
  }

  /// Get token symbol safely
  String getTokenSymbol(Map<String, dynamic> tokenData) {
    return (tokenData['symbol'] as String?) ?? 'UNKNOWN';
  }

  /// Get token name safely
  String getTokenName(Map<String, dynamic> tokenData) {
    return (tokenData['name'] as String?) ?? 'Unknown Token';
  }

  /// Get token image URL safely
  String? getTokenImageUrl(Map<String, dynamic> tokenData) {
    return tokenData['image_url'] as String?;
  }

  /// Dispose resources
  void dispose() {
    debugPrint('🧹 Disposing TokenService');
    _tokenStreamSubscription?.cancel();
    _tokenStreamController.close();
  }
}
