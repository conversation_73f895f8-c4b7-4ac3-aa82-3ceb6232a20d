class BotDefaultPreferences {
  // Basic bot settings
  final String defaultStrategy;
  final double defaultInvestmentAmount;
  final double defaultStopLoss;
  final double defaultTakeProfit;
  final double defaultRiskPercentage;
  final bool defaultTrading24_7;
  final String defaultTradingStartTime;
  final String defaultTradingEndTime;
  final int defaultMaxDailyTrades;
  final double? defaultAllTimeHighValue;
  final double? defaultMinMarketCap;
  final double? defaultMaxMarketCap;
  final List<String> defaultRiskLevels;
  final bool defaultStartImmediately;

  // RSI settings
  final bool defaultUseRsi;
  final int defaultRsiPeriod;
  final int defaultRsiOverbought;
  final int defaultRsiOversold;

  // MACD settings
  final bool defaultUseMacd;
  final int defaultMacdFastPeriod;
  final int defaultMacdSlowPeriod;
  final int defaultMacdSignalPeriod;

  // Bollinger Bands settings
  final bool defaultUseBollinger;
  final int defaultBollingerPeriod;
  final double defaultBollingerStdDev;

  // EMA settings
  final bool defaultUseEma;
  final int defaultEmaFastPeriod;
  final int defaultEmaSlowPeriod;

  // KST settings
  final bool defaultUseKst;
  final List<int> defaultKstRocPeriods;
  final List<int> defaultKstSmaPeriods;
  final int defaultKstSignalPeriod;

  // Volume Profile settings
  final bool defaultUseVolumeProfile;

  BotDefaultPreferences({
    required this.defaultStrategy,
    required this.defaultInvestmentAmount,
    required this.defaultStopLoss,
    required this.defaultTakeProfit,
    required this.defaultRiskPercentage,
    required this.defaultTrading24_7,
    required this.defaultTradingStartTime,
    required this.defaultTradingEndTime,
    required this.defaultMaxDailyTrades,
    this.defaultAllTimeHighValue,
    this.defaultMinMarketCap,
    this.defaultMaxMarketCap,
    required this.defaultRiskLevels,
    required this.defaultStartImmediately,
    required this.defaultUseRsi,
    required this.defaultRsiPeriod,
    required this.defaultRsiOverbought,
    required this.defaultRsiOversold,
    required this.defaultUseMacd,
    required this.defaultMacdFastPeriod,
    required this.defaultMacdSlowPeriod,
    required this.defaultMacdSignalPeriod,
    required this.defaultUseBollinger,
    required this.defaultBollingerPeriod,
    required this.defaultBollingerStdDev,
    required this.defaultUseEma,
    required this.defaultEmaFastPeriod,
    required this.defaultEmaSlowPeriod,
    required this.defaultUseKst,
    required this.defaultKstRocPeriods,
    required this.defaultKstSmaPeriods,
    required this.defaultKstSignalPeriod,
    required this.defaultUseVolumeProfile,
  });

  factory BotDefaultPreferences.fromJson(Map<String, dynamic> json) {
    return BotDefaultPreferences(
      defaultStrategy: json['default_strategy'] ?? 'DCA',
      defaultInvestmentAmount: (json['default_investment_amount'] ?? 1.0)
          .toDouble(),
      defaultStopLoss: (json['default_stop_loss'] ?? 5.0).toDouble(),
      defaultTakeProfit: (json['default_take_profit'] ?? 10.0).toDouble(),
      defaultRiskPercentage: (json['default_risk_percentage'] ?? 2.0)
          .toDouble(),
      defaultTrading24_7: json['default_trading_24_7'] ?? true,
      defaultTradingStartTime: json['default_trading_start_time'] ?? '09:00',
      defaultTradingEndTime: json['default_trading_end_time'] ?? '17:00',
      defaultMaxDailyTrades: json['default_max_daily_trades'] ?? 50,
      defaultAllTimeHighValue: json['default_all_time_high_value']?.toDouble(),
      defaultMinMarketCap: json['default_min_market_cap']?.toDouble(),
      defaultMaxMarketCap: json['default_max_market_cap']?.toDouble(),
      defaultRiskLevels: List<String>.from(
        json['default_risk_levels'] ?? ['Low', 'Medium'],
      ),
      defaultStartImmediately: json['default_start_immediately'] ?? true,
      defaultUseRsi: json['default_use_rsi'] ?? true,
      defaultRsiPeriod: json['default_rsi_period'] ?? 14,
      defaultRsiOverbought: json['default_rsi_overbought'] ?? 70,
      defaultRsiOversold: json['default_rsi_oversold'] ?? 30,
      defaultUseMacd: json['default_use_macd'] ?? false,
      defaultMacdFastPeriod: json['default_macd_fast_period'] ?? 12,
      defaultMacdSlowPeriod: json['default_macd_slow_period'] ?? 26,
      defaultMacdSignalPeriod: json['default_macd_signal_period'] ?? 9,
      defaultUseBollinger: json['default_use_bollinger'] ?? false,
      defaultBollingerPeriod: json['default_bollinger_period'] ?? 20,
      defaultBollingerStdDev: (json['default_bollinger_std_dev'] ?? 2.0)
          .toDouble(),
      defaultUseEma: json['default_use_ema'] ?? false,
      defaultEmaFastPeriod: json['default_ema_fast_period'] ?? 12,
      defaultEmaSlowPeriod: json['default_ema_slow_period'] ?? 26,
      defaultUseKst: json['default_use_kst'] ?? false,
      defaultKstRocPeriods: List<int>.from(
        json['default_kst_roc_periods'] ?? [10, 15, 20, 30],
      ),
      defaultKstSmaPeriods: List<int>.from(
        json['default_kst_sma_periods'] ?? [10, 10, 10, 15],
      ),
      defaultKstSignalPeriod: json['default_kst_signal_period'] ?? 9,
      defaultUseVolumeProfile: json['default_use_volume_profile'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'default_strategy': defaultStrategy,
      'default_investment_amount': defaultInvestmentAmount,
      'default_stop_loss': defaultStopLoss,
      'default_take_profit': defaultTakeProfit,
      'default_risk_percentage': defaultRiskPercentage,
      'default_trading_24_7': defaultTrading24_7,
      'default_trading_start_time': defaultTradingStartTime,
      'default_trading_end_time': defaultTradingEndTime,
      'default_max_daily_trades': defaultMaxDailyTrades,
      'default_all_time_high_value': defaultAllTimeHighValue,
      'default_min_market_cap': defaultMinMarketCap,
      'default_max_market_cap': defaultMaxMarketCap,
      'default_risk_levels': defaultRiskLevels,
      'default_start_immediately': defaultStartImmediately,
      'default_use_rsi': defaultUseRsi,
      'default_rsi_period': defaultRsiPeriod,
      'default_rsi_overbought': defaultRsiOverbought,
      'default_rsi_oversold': defaultRsiOversold,
      'default_use_macd': defaultUseMacd,
      'default_macd_fast_period': defaultMacdFastPeriod,
      'default_macd_slow_period': defaultMacdSlowPeriod,
      'default_macd_signal_period': defaultMacdSignalPeriod,
      'default_use_bollinger': defaultUseBollinger,
      'default_bollinger_period': defaultBollingerPeriod,
      'default_bollinger_std_dev': defaultBollingerStdDev,
      'default_use_ema': defaultUseEma,
      'default_ema_fast_period': defaultEmaFastPeriod,
      'default_ema_slow_period': defaultEmaSlowPeriod,
      'default_use_kst': defaultUseKst,
      'default_kst_roc_periods': defaultKstRocPeriods,
      'default_kst_sma_periods': defaultKstSmaPeriods,
      'default_kst_signal_period': defaultKstSignalPeriod,
      'default_use_volume_profile': defaultUseVolumeProfile,
    };
  }

  factory BotDefaultPreferences.createDefault() {
    return BotDefaultPreferences(
      defaultStrategy: 'DCA',
      defaultInvestmentAmount: 1.0,
      defaultStopLoss: 5.0,
      defaultTakeProfit: 10.0,
      defaultRiskPercentage: 2.0,
      defaultTrading24_7: true,
      defaultTradingStartTime: '09:00',
      defaultTradingEndTime: '17:00',
      defaultMaxDailyTrades: 50,
      defaultAllTimeHighValue: 150000.0,
      defaultMinMarketCap: 100000.0,
      defaultMaxMarketCap: 1000000.0,
      defaultRiskLevels: ['Low', 'Medium'],
      defaultStartImmediately: true,
      defaultUseRsi: true,
      defaultRsiPeriod: 14,
      defaultRsiOverbought: 70,
      defaultRsiOversold: 30,
      defaultUseMacd: false,
      defaultMacdFastPeriod: 12,
      defaultMacdSlowPeriod: 26,
      defaultMacdSignalPeriod: 9,
      defaultUseBollinger: false,
      defaultBollingerPeriod: 20,
      defaultBollingerStdDev: 2.0,
      defaultUseEma: false,
      defaultEmaFastPeriod: 12,
      defaultEmaSlowPeriod: 26,
      defaultUseKst: false,
      defaultKstRocPeriods: [10, 15, 20, 30],
      defaultKstSmaPeriods: [10, 10, 10, 15],
      defaultKstSignalPeriod: 9,
      defaultUseVolumeProfile: false,
    );
  }

  BotDefaultPreferences copyWith({
    String? defaultStrategy,
    double? defaultInvestmentAmount,
    double? defaultStopLoss,
    double? defaultTakeProfit,
    double? defaultRiskPercentage,
    bool? defaultTrading24_7,
    String? defaultTradingStartTime,
    String? defaultTradingEndTime,
    int? defaultMaxDailyTrades,
    double? defaultAllTimeHighValue,
    double? defaultMinMarketCap,
    double? defaultMaxMarketCap,
    List<String>? defaultRiskLevels,
    bool? defaultStartImmediately,
    bool? defaultUseRsi,
    int? defaultRsiPeriod,
    int? defaultRsiOverbought,
    int? defaultRsiOversold,
    bool? defaultUseMacd,
    int? defaultMacdFastPeriod,
    int? defaultMacdSlowPeriod,
    int? defaultMacdSignalPeriod,
    bool? defaultUseBollinger,
    int? defaultBollingerPeriod,
    double? defaultBollingerStdDev,
    bool? defaultUseEma,
    int? defaultEmaFastPeriod,
    int? defaultEmaSlowPeriod,
    bool? defaultUseKst,
    List<int>? defaultKstRocPeriods,
    List<int>? defaultKstSmaPeriods,
    int? defaultKstSignalPeriod,
    bool? defaultUseVolumeProfile,
  }) {
    return BotDefaultPreferences(
      defaultStrategy: defaultStrategy ?? this.defaultStrategy,
      defaultInvestmentAmount:
          defaultInvestmentAmount ?? this.defaultInvestmentAmount,
      defaultStopLoss: defaultStopLoss ?? this.defaultStopLoss,
      defaultTakeProfit: defaultTakeProfit ?? this.defaultTakeProfit,
      defaultRiskPercentage:
          defaultRiskPercentage ?? this.defaultRiskPercentage,
      defaultTrading24_7: defaultTrading24_7 ?? this.defaultTrading24_7,
      defaultTradingStartTime:
          defaultTradingStartTime ?? this.defaultTradingStartTime,
      defaultTradingEndTime:
          defaultTradingEndTime ?? this.defaultTradingEndTime,
      defaultMaxDailyTrades:
          defaultMaxDailyTrades ?? this.defaultMaxDailyTrades,
      defaultAllTimeHighValue:
          defaultAllTimeHighValue ?? this.defaultAllTimeHighValue,
      defaultMinMarketCap: defaultMinMarketCap ?? this.defaultMinMarketCap,
      defaultMaxMarketCap: defaultMaxMarketCap ?? this.defaultMaxMarketCap,
      defaultRiskLevels: defaultRiskLevels ?? this.defaultRiskLevels,
      defaultStartImmediately:
          defaultStartImmediately ?? this.defaultStartImmediately,
      defaultUseRsi: defaultUseRsi ?? this.defaultUseRsi,
      defaultRsiPeriod: defaultRsiPeriod ?? this.defaultRsiPeriod,
      defaultRsiOverbought: defaultRsiOverbought ?? this.defaultRsiOverbought,
      defaultRsiOversold: defaultRsiOversold ?? this.defaultRsiOversold,
      defaultUseMacd: defaultUseMacd ?? this.defaultUseMacd,
      defaultMacdFastPeriod:
          defaultMacdFastPeriod ?? this.defaultMacdFastPeriod,
      defaultMacdSlowPeriod:
          defaultMacdSlowPeriod ?? this.defaultMacdSlowPeriod,
      defaultMacdSignalPeriod:
          defaultMacdSignalPeriod ?? this.defaultMacdSignalPeriod,
      defaultUseBollinger: defaultUseBollinger ?? this.defaultUseBollinger,
      defaultBollingerPeriod:
          defaultBollingerPeriod ?? this.defaultBollingerPeriod,
      defaultBollingerStdDev:
          defaultBollingerStdDev ?? this.defaultBollingerStdDev,
      defaultUseEma: defaultUseEma ?? this.defaultUseEma,
      defaultEmaFastPeriod: defaultEmaFastPeriod ?? this.defaultEmaFastPeriod,
      defaultEmaSlowPeriod: defaultEmaSlowPeriod ?? this.defaultEmaSlowPeriod,
      defaultUseKst: defaultUseKst ?? this.defaultUseKst,
      defaultKstRocPeriods: defaultKstRocPeriods ?? this.defaultKstRocPeriods,
      defaultKstSmaPeriods: defaultKstSmaPeriods ?? this.defaultKstSmaPeriods,
      defaultKstSignalPeriod:
          defaultKstSignalPeriod ?? this.defaultKstSignalPeriod,
      defaultUseVolumeProfile:
          defaultUseVolumeProfile ?? this.defaultUseVolumeProfile,
    );
  }
}
