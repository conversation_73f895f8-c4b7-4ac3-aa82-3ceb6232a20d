import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../models/bot_default_preferences.dart';
import '../services/user_preferences_service.dart';
import '../utils/toast_utils.dart';

class BotSettingsPage extends StatefulWidget {
  const BotSettingsPage({super.key});

  @override
  State<BotSettingsPage> createState() => _BotSettingsPageState();
}

class _BotSettingsPageState extends State<BotSettingsPage> {
  final UserPreferencesService _preferencesService = UserPreferencesService();
  final ScrollController _scrollController = ScrollController();

  BotDefaultPreferences? _preferences;
  bool _isLoading = true;
  bool _isSaving = false;

  // Controllers for text fields
  final TextEditingController _investmentController = TextEditingController();
  final TextEditingController _stopLossController = TextEditingController();
  final TextEditingController _takeProfitController = TextEditingController();
  final TextEditingController _riskPercentageController =
      TextEditingController();
  final TextEditingController _maxTradesController = TextEditingController();
  final TextEditingController _athController = TextEditingController();
  final TextEditingController _minMarketCapController = TextEditingController();
  final TextEditingController _maxMarketCapController = TextEditingController();
  final TextEditingController _startTimeController = TextEditingController();
  final TextEditingController _endTimeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadPreferences();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _investmentController.dispose();
    _stopLossController.dispose();
    _takeProfitController.dispose();
    _riskPercentageController.dispose();
    _maxTradesController.dispose();
    _athController.dispose();
    _minMarketCapController.dispose();
    _maxMarketCapController.dispose();
    _startTimeController.dispose();
    _endTimeController.dispose();
    super.dispose();
  }

  Future<void> _loadPreferences() async {
    setState(() => _isLoading = true);

    try {
      final preferences = await _preferencesService.getBotDefaultPreferences();
      if (preferences != null) {
        setState(() {
          _preferences = preferences;
          _populateControllers();
        });
      }
    } catch (e) {
      debugPrint('Error loading preferences: $e');
      ToastUtils.showError(context, 'Failed to load bot settings');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _populateControllers() {
    if (_preferences == null) return;

    _investmentController.text = _preferences!.defaultInvestmentAmount
        .toString();
    _stopLossController.text = _preferences!.defaultStopLoss.toString();
    _takeProfitController.text = _preferences!.defaultTakeProfit.toString();
    _riskPercentageController.text = _preferences!.defaultRiskPercentage
        .toString();
    _maxTradesController.text = _preferences!.defaultMaxDailyTrades.toString();
    _startTimeController.text = _preferences!.defaultTradingStartTime;
    _endTimeController.text = _preferences!.defaultTradingEndTime;

    // Convert K values for display
    if (_preferences!.defaultAllTimeHighValue != null) {
      _athController.text = (_preferences!.defaultAllTimeHighValue! / 1000)
          .toString();
    }
    if (_preferences!.defaultMinMarketCap != null) {
      _minMarketCapController.text = (_preferences!.defaultMinMarketCap! / 1000)
          .toString();
    }
    if (_preferences!.defaultMaxMarketCap != null) {
      _maxMarketCapController.text = (_preferences!.defaultMaxMarketCap! / 1000)
          .toString();
    }
  }

  Future<void> _savePreferences() async {
    if (_preferences == null) return;

    setState(() => _isSaving = true);

    try {
      // Update preferences with current values
      final updatedPreferences = _preferences!.copyWith(
        defaultInvestmentAmount:
            double.tryParse(_investmentController.text) ??
            _preferences!.defaultInvestmentAmount,
        defaultStopLoss:
            double.tryParse(_stopLossController.text) ??
            _preferences!.defaultStopLoss,
        defaultTakeProfit:
            double.tryParse(_takeProfitController.text) ??
            _preferences!.defaultTakeProfit,
        defaultRiskPercentage:
            double.tryParse(_riskPercentageController.text) ??
            _preferences!.defaultRiskPercentage,
        defaultMaxDailyTrades:
            int.tryParse(_maxTradesController.text) ??
            _preferences!.defaultMaxDailyTrades,
        defaultTradingStartTime: _startTimeController.text.isNotEmpty
            ? _startTimeController.text
            : _preferences!.defaultTradingStartTime,
        defaultTradingEndTime: _endTimeController.text.isNotEmpty
            ? _endTimeController.text
            : _preferences!.defaultTradingEndTime,
        defaultAllTimeHighValue: _athController.text.isNotEmpty
            ? (double.tryParse(_athController.text) ?? 0) * 1000
            : _preferences!.defaultAllTimeHighValue,
        defaultMinMarketCap: _minMarketCapController.text.isNotEmpty
            ? (double.tryParse(_minMarketCapController.text) ?? 0) * 1000
            : _preferences!.defaultMinMarketCap,
        defaultMaxMarketCap: _maxMarketCapController.text.isNotEmpty
            ? (double.tryParse(_maxMarketCapController.text) ?? 0) * 1000
            : _preferences!.defaultMaxMarketCap,
      );

      final success = await _preferencesService.updateBotDefaultPreferences(
        updatedPreferences,
      );

      if (success) {
        setState(() => _preferences = updatedPreferences);
        ToastUtils.showSuccess(context, 'Bot settings saved successfully');
      } else {
        ToastUtils.showError(context, 'Failed to save bot settings');
      }
    } catch (e) {
      debugPrint('Error saving preferences: $e');
      ToastUtils.showError(context, 'Failed to save bot settings');
    } finally {
      setState(() => _isSaving = false);
    }
  }

  Future<void> _resetToDefaults() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1A1A1A),
        title: const Text(
          'Reset to Defaults',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'Are you sure you want to reset all bot settings to default values? This action cannot be undone.',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            style: TextButton.styleFrom(foregroundColor: Colors.white70),
            child: const Text('Cancel'),
          ),
          Container(
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Colors.red, Colors.redAccent],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.red.withValues(alpha: 0.3),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Reset',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() => _isSaving = true);

      try {
        final success = await _preferencesService
            .resetBotPreferencesToDefaults();
        if (success) {
          await _loadPreferences();
          ToastUtils.showSuccess(context, 'Bot settings reset to defaults');
        } else {
          ToastUtils.showError(context, 'Failed to reset bot settings');
        }
      } catch (e) {
        debugPrint('Error resetting preferences: $e');
        ToastUtils.showError(context, 'Failed to reset bot settings');
      } finally {
        setState(() => _isSaving = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF0A0A0A), Color(0xFF1A1A2E), Color(0xFF16213E)],
          ),
        ),
        child: Scaffold(
          backgroundColor: Colors.transparent,
          appBar: AppBar(
            backgroundColor: const Color(0xFF0A0A0A),
            elevation: 0,
            leading: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(
                LucideIcons.arrowLeft,
                color: Colors.white,
                size: 20,
              ),
            ),
            title: const Text(
              'Bot Default Settings',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            actions: [
              if (!_isLoading)
                IconButton(
                  onPressed: _resetToDefaults,
                  icon: const Icon(
                    LucideIcons.rotateCcw,
                    color: Colors.white,
                    size: 20,
                  ),
                  tooltip: 'Reset to Defaults',
                ),
            ],
          ),
          body: _isLoading
              ? const Center(
                  child: CircularProgressIndicator(color: Colors.blue),
                )
              : SingleChildScrollView(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Description
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.blue.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              LucideIcons.info,
                              color: Colors.blue,
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'Configure your default bot settings. These values will be used as defaults when creating new trading bots.',
                                style: TextStyle(
                                  color: Colors.blue.shade100,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Basic Settings Section
                      _buildBasicSettings(),

                      const SizedBox(height: 24),

                      // Trading Hours Section
                      _buildTradingHoursSettings(),

                      const SizedBox(height: 24),

                      // Market Cap Settings Section
                      _buildMarketCapSettings(),

                      const SizedBox(height: 24),

                      // Indicator Settings Section
                      _buildIndicatorSettings(),

                      const SizedBox(height: 32),

                      // Save Button with Gradient
                      SizedBox(
                        width: double.infinity,
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: _isSaving
                                ? LinearGradient(
                                    colors: [
                                      Colors.grey.withValues(alpha: 0.5),
                                      Colors.grey.withValues(alpha: 0.3),
                                    ],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  )
                                : const LinearGradient(
                                    colors: [Colors.blue, Colors.cyan],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: _isSaving
                                ? null
                                : [
                                    BoxShadow(
                                      color: Colors.blue.withValues(alpha: 0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                          ),
                          child: ElevatedButton(
                            onPressed: _isSaving ? null : _savePreferences,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              foregroundColor: Colors.white,
                              shadowColor: Colors.transparent,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: _isSaving
                                ? const SizedBox(
                                    height: 20,
                                    width: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Colors.white,
                                    ),
                                  )
                                : const Text(
                                    'Save Settings',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildBasicSettings() {
    return _buildSection('Basic Settings', LucideIcons.settings, [
      _buildStrategyDropdown(),
      const SizedBox(height: 16),
      _buildNumberField(
        'Investment Amount (SOL)',
        _investmentController,
        'Range: 0.01 - 10.0 SOL',
        min: 0.01,
        max: 10.0,
        suffix: 'SOL',
      ),
      const SizedBox(height: 16),
      _buildNumberField(
        'Stop Loss (%)',
        _stopLossController,
        'Range: 1 - 50%',
        min: 1.0,
        max: 50.0,
        suffix: '%',
      ),
      const SizedBox(height: 16),
      _buildNumberField(
        'Take Profit (%)',
        _takeProfitController,
        'Range: 1 - 100%',
        min: 1.0,
        max: 100.0,
        suffix: '%',
      ),
      const SizedBox(height: 16),
      _buildNumberField(
        'Max Daily Trades',
        _maxTradesController,
        'Range: 1 - 1000 trades',
        min: 1.0,
        max: 1000.0,
      ),
      const SizedBox(height: 16),
      _buildKValueField(
        'All Time High (K)',
        _athController,
        'Default all time high value in thousands',
      ),
      const SizedBox(height: 16),
      _buildSwitchField(
        'Start Immediately',
        _preferences?.defaultStartImmediately ?? true,
        'Start trading immediately after bot creation',
        (value) {
          setState(() {
            _preferences = _preferences?.copyWith(
              defaultStartImmediately: value,
            );
          });
        },
      ),
    ]);
  }

  Widget _buildTradingHoursSettings() {
    return _buildSection('Trading Hours', LucideIcons.clock, [
      _buildSwitchField(
        '24/7 Trading',
        _preferences?.defaultTrading24_7 ?? true,
        'Enable 24/7 trading by default',
        (value) {
          setState(() {
            _preferences = _preferences?.copyWith(defaultTrading24_7: value);
          });
        },
      ),
      if (_preferences?.defaultTrading24_7 == false) ...[
        const SizedBox(height: 16),
        _buildTimeField(
          'Start Time',
          _startTimeController,
          'Default trading start time',
        ),
        const SizedBox(height: 16),
        _buildTimeField(
          'End Time',
          _endTimeController,
          'Default trading end time',
        ),
      ],
    ]);
  }

  Widget _buildMarketCapSettings() {
    return _buildSection('Market Cap Range', LucideIcons.trendingUp, [
      _buildKValueField(
        'Minimum Market Cap (K)',
        _minMarketCapController,
        'Default minimum market cap in thousands',
      ),
      const SizedBox(height: 16),
      _buildKValueField(
        'Maximum Market Cap (K)',
        _maxMarketCapController,
        'Default maximum market cap in thousands',
      ),
    ]);
  }

  Widget _buildIndicatorSettings() {
    return _buildSection('Default Indicators', LucideIcons.activity, [
      _buildIndicatorToggle(
        'RSI',
        _preferences?.defaultUseRsi ?? true,
        'Relative Strength Index',
        (value) {
          setState(() {
            _preferences = _preferences?.copyWith(defaultUseRsi: value);
          });
        },
      ),
      const SizedBox(height: 16),
      _buildIndicatorToggle(
        'MACD',
        _preferences?.defaultUseMacd ?? false,
        'Moving Average Convergence Divergence',
        (value) {
          setState(() {
            _preferences = _preferences?.copyWith(defaultUseMacd: value);
          });
        },
      ),
      const SizedBox(height: 16),
      _buildIndicatorToggle(
        'Bollinger Bands',
        _preferences?.defaultUseBollinger ?? false,
        'Bollinger Bands indicator',
        (value) {
          setState(() {
            _preferences = _preferences?.copyWith(defaultUseBollinger: value);
          });
        },
      ),
      const SizedBox(height: 16),
      _buildIndicatorToggle(
        'EMA',
        _preferences?.defaultUseEma ?? false,
        'Exponential Moving Average',
        (value) {
          setState(() {
            _preferences = _preferences?.copyWith(defaultUseEma: value);
          });
        },
      ),
      const SizedBox(height: 16),
      _buildIndicatorToggle(
        'KST',
        _preferences?.defaultUseKst ?? false,
        'Know Sure Thing Oscillator',
        (value) {
          setState(() {
            _preferences = _preferences?.copyWith(defaultUseKst: value);
          });
        },
      ),
      const SizedBox(height: 16),
      _buildIndicatorToggle(
        'Volume Profile',
        _preferences?.defaultUseVolumeProfile ?? false,
        'Volume Profile indicator',
        (value) {
          setState(() {
            _preferences = _preferences?.copyWith(
              defaultUseVolumeProfile: value,
            );
          });
        },
      ),
    ]);
  }

  Widget _buildSection(String title, IconData icon, List<Widget> children) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: Colors.blue, size: 20),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ...children,
        ],
      ),
    );
  }

  Widget _buildStrategyDropdown() {
    final strategies = ['DCA', 'Scalping', 'Grid Trading', 'Momentum'];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _preferences?.defaultStrategy ?? 'DCA',
          isExpanded: true,
          dropdownColor: const Color(0xFF1A1A1A),
          style: const TextStyle(color: Colors.white),
          items: strategies.map((strategy) {
            return DropdownMenuItem<String>(
              value: strategy,
              child: Text(strategy),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _preferences = _preferences?.copyWith(defaultStrategy: value);
              });
            }
          },
        ),
      ),
    );
  }

  Widget _buildNumberField(
    String label,
    TextEditingController controller,
    String hint, {
    double? min,
    double? max,
    String? suffix,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          keyboardType: TextInputType.numberWithOptions(decimal: true),
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            suffixText: suffix,
            suffixStyle: const TextStyle(color: Colors.blue),
            hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.5)),
            filled: true,
            fillColor: Colors.white.withValues(alpha: 0.05),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Colors.white.withValues(alpha: 0.2),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Colors.white.withValues(alpha: 0.2),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.blue),
            ),
          ),
          onChanged: (value) {
            if (min != null || max != null) {
              final numValue = double.tryParse(value);
              if (numValue != null) {
                if (min != null && numValue < min) {
                  controller.text = min.toString();
                  controller.selection = TextSelection.fromPosition(
                    TextPosition(offset: controller.text.length),
                  );
                } else if (max != null && numValue > max) {
                  controller.text = max.toString();
                  controller.selection = TextSelection.fromPosition(
                    TextPosition(offset: controller.text.length),
                  );
                }
              }
            }
          },
        ),
        if (min != null || max != null)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              'Range: ${min ?? 'No limit'} - ${max ?? 'No limit'}',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.6),
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildKValueField(
    String label,
    TextEditingController controller,
    String hint,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: controller,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: hint,
                  hintStyle: TextStyle(
                    color: Colors.white.withValues(alpha: 0.5),
                  ),
                  filled: true,
                  fillColor: Colors.white.withValues(alpha: 0.05),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Colors.white.withValues(alpha: 0.2),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Colors.white.withValues(alpha: 0.2),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.blue),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.blue.withValues(alpha: 0.5),
                  width: 1,
                ),
              ),
              child: const Text(
                'K',
                style: TextStyle(
                  color: Colors.blue,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTimeField(
    String label,
    TextEditingController controller,
    String hint,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.5)),
            filled: true,
            fillColor: Colors.white.withValues(alpha: 0.05),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Colors.white.withValues(alpha: 0.2),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Colors.white.withValues(alpha: 0.2),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.blue),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSwitchField(
    String label,
    bool value,
    String description,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Colors.blue,
            inactiveThumbColor: Colors.grey,
            inactiveTrackColor: Colors.grey.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }

  Widget _buildIndicatorToggle(
    String label,
    bool value,
    String description,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: value
            ? Colors.blue.withValues(alpha: 0.1)
            : Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: value
              ? Colors.blue.withValues(alpha: 0.3)
              : Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: value ? Colors.blue.shade100 : Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    color: value
                        ? Colors.blue.shade200.withValues(alpha: 0.8)
                        : Colors.white.withValues(alpha: 0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Colors.blue,
            inactiveThumbColor: Colors.grey,
            inactiveTrackColor: Colors.grey.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }
}
