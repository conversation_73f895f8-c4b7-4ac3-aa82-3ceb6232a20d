import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:url_launcher/url_launcher.dart';

class PhantomWalletService {
  // Singleton instance
  static final PhantomWalletService _instance = PhantomWalletService._();
  factory PhantomWalletService() => _instance;
  PhantomWalletService._();

  // Constants
  static const String _phantomScheme = 'phantom://';
  static const String _redirectScheme = 'dextrip://';

  String? _publicKey;
  String? _session;
  String? _sharedSecret;
  bool _isConnected = false;

  // Getters
  bool get isConnected => _isConnected;
  String? get publicKey => _publicKey;
  String? get walletAddress => _publicKey;

  // Generate a random session ID
  String _generateSession() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Url.encode(bytes);
  }

  // Generate encryption keypair (simplified for demo)
  Map<String, String> _generateKeypair() {
    final random = Random.secure();
    final privateKey = List<int>.generate(32, (i) => random.nextInt(256));
    final publicKey = List<int>.generate(32, (i) => random.nextInt(256));

    return {
      'privateKey': base64Url.encode(privateKey),
      'publicKey': base64Url.encode(publicKey),
    };
  }

  // Connect to Phantom wallet
  Future<bool> connect() async {
    try {
      // Check if Phantom is installed
      final phantomUrl = Uri.parse('phantom://');
      if (!await canLaunchUrl(phantomUrl)) {
        throw Exception('Phantom wallet is not installed');
      }

      // Generate session and encryption keys
      _session = _generateSession();
      final keypair = _generateKeypair();

      // Create connection URL
      final connectParams = {
        'dapp_encryption_public_key': keypair['publicKey'],
        'cluster': 'mainnet-beta',
        'app_url': 'https://dextrip.app',
        'redirect_link': '${_redirectScheme}onConnect',
      };

      final connectUrl = Uri.parse(
        '${_phantomScheme}v1/connect?dapp_encryption_public_key=${connectParams['dapp_encryption_public_key']}&cluster=${connectParams['cluster']}&app_url=${connectParams['app_url']}&redirect_link=${connectParams['redirect_link']}',
      );

      // Launch Phantom wallet
      final launched = await launchUrl(
        connectUrl,
        mode: LaunchMode.externalApplication,
      );

      if (!launched) {
        throw Exception('Failed to launch Phantom wallet');
      }

      return true;
    } catch (e) {
      debugPrint('Error connecting to Phantom: $e');
      return false;
    }
  }

  // Handle connection response from Phantom
  bool handleConnectionResponse(Uri uri) {
    try {
      if (uri.scheme != 'dextrip' || uri.host != 'onConnect') {
        return false;
      }

      final params = uri.queryParameters;

      if (params.containsKey('errorCode')) {
        debugPrint('Phantom connection error: ${params['errorMessage']}');
        return false;
      }

      // Extract connection data
      _publicKey = params['phantom_encryption_public_key'];
      _sharedSecret = params['nonce'];

      if (_publicKey != null) {
        _isConnected = true;
        debugPrint('Successfully connected to Phantom wallet');
        debugPrint('Public Key: $_publicKey');
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Error handling Phantom response: $e');
      return false;
    }
  }

  // Disconnect from wallet
  Future<void> disconnect() async {
    try {
      if (!_isConnected) return;

      final disconnectUrl = Uri.parse(
        '${_phantomScheme}v1/disconnect?dapp_encryption_public_key=${_sharedSecret}&redirect_link=${_redirectScheme}onDisconnect',
      );

      await launchUrl(disconnectUrl, mode: LaunchMode.externalApplication);

      // Clear local state
      _publicKey = null;
      _session = null;
      _sharedSecret = null;
      _isConnected = false;

      debugPrint('Disconnected from Phantom wallet');
    } catch (e) {
      debugPrint('Error disconnecting from Phantom: $e');
    }
  }

  // Sign a transaction (placeholder for future implementation)
  Future<String?> signTransaction(String transaction) async {
    try {
      if (!_isConnected) {
        throw Exception('Wallet not connected');
      }

      final signParams = {'transaction': transaction, 'session': _session};

      final encodedParams = Uri.encodeComponent(jsonEncode(signParams));
      final signUrl = Uri.parse(
        '${_phantomScheme}v1/signTransaction?transaction=$encodedParams&redirect_link=${_redirectScheme}onSignTransaction',
      );

      final launched = await launchUrl(
        signUrl,
        mode: LaunchMode.externalApplication,
      );

      if (!launched) {
        throw Exception('Failed to launch Phantom for signing');
      }

      return null; // Will be handled by deep link response
    } catch (e) {
      debugPrint('Error signing transaction: $e');
      return null;
    }
  }

  // Sign a message (placeholder for future implementation)
  Future<String?> signMessage(String message) async {
    try {
      if (!_isConnected) {
        throw Exception('Wallet not connected');
      }

      final signParams = {
        'message': base64Encode(utf8.encode(message)),
        'display': 'utf8',
      };

      final signUrl = Uri.parse(
        '${_phantomScheme}v1/signMessage?message=${signParams['message']}&display=${signParams['display']}&redirect_link=${_redirectScheme}onSignMessage',
      );

      final launched = await launchUrl(
        signUrl,
        mode: LaunchMode.externalApplication,
      );

      if (!launched) {
        throw Exception('Failed to launch Phantom for message signing');
      }

      return null; // Will be handled by deep link response
    } catch (e) {
      debugPrint('Error signing message: $e');
      return null;
    }
  }

  // Check if Phantom wallet is installed
  static Future<bool> isPhantomInstalled() async {
    try {
      final phantomUrl = Uri.parse('phantom://');
      return await canLaunchUrl(phantomUrl);
    } catch (e) {
      return false;
    }
  }

  // Open Phantom wallet app store page if not installed
  static Future<void> openPhantomStore() async {
    try {
      const appStoreUrl =
          'https://apps.apple.com/app/phantom-solana-wallet/id1598432977';
      const playStoreUrl =
          'https://play.google.com/store/apps/details?id=app.phantom';

      final url = defaultTargetPlatform == TargetPlatform.iOS
          ? appStoreUrl
          : playStoreUrl;
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } catch (e) {
      debugPrint('Error opening Phantom store: $e');
    }
  }
}
