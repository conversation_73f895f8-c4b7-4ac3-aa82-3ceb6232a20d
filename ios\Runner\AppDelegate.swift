import Flutter
import UIKit

@main
@objc class AppDelegate: FlutterAppDelegate {
  private var deepLinkChannel: FlutterMethodChannel?

  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)

    // Set up deep link method channel
    let controller = window?.rootViewController as! FlutterViewController
    deepLinkChannel = FlutterMethodChannel(
      name: "dextrip.deeplink/channel",
      binaryMessenger: controller.binaryMessenger
    )

    deepLinkChannel?.setMethodCallHandler { [weak self] (call, result) in
      switch call.method {
      case "getInitialLink":
        // Handle initial link if app was opened via deep link
        result(nil)
      default:
        result(FlutterMethodNotImplemented)
      }
    }

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  // Handle deep links when app is already running
  override func application(
    _ app: UIApplication,
    open url: URL,
    options: [UIApplication.OpenURLOptionsKey : Any] = [:]
  ) -> Bool {
    // Send deep link to Flutter
    deepLinkChannel?.invokeMethod("onNewLink", arguments: url.absoluteString)
    return true
  }

  // Handle universal links
  override func application(
    _ application: UIApplication,
    continue userActivity: NSUserActivity,
    restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void
  ) -> Bool {
    if userActivity.activityType == NSUserActivityTypeBrowsingWeb,
       let url = userActivity.webpageURL {
      deepLinkChannel?.invokeMethod("onNewLink", arguments: url.absoluteString)
      return true
    }
    return false
  }
}
