import 'dart:async';
import 'dart:convert';
import 'package:dextrip_app/services/supabase_auth_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:webview_universal_plus/webview_universal.dart';
import '../models/coin_model.dart';
import '../services/app_lifecycle_service.dart';
import '../services/supabase_coin_service.dart';
import '../widgets/gradient_button.dart';

import '../widgets/ai_prediction.dart';
import '../widgets/bot_config_modal.dart';
import '../utils/currency_formatter.dart';
import '../utils/responsive_helper.dart';

class CoinDetailPage extends StatefulWidget {
  final String symbol;
  final CoinData coin;

  const CoinDetailPage({super.key, required this.symbol, required this.coin});

  @override
  State<CoinDetailPage> createState() => _CoinDetailPageState();
}

class _CoinDetailPageState extends State<CoinDetailPage>
    with TickerProviderStateMixin {
  final AppLifecycleService _lifecycleService = AppLifecycleService();
  final SupabaseCoinService _supabaseCoinService = SupabaseCoinService();
  final SupabaseAuthService _authService = SupabaseAuthService();
  late CoinData _currentCoin;
  StreamSubscription<List<CoinData>>? _coinStreamSubscription;
  StreamSubscription<CoinData>? _singleCoinStreamSubscription;

  Timer? _priceUpdateTimer;
  bool _subscriptionsPaused = false;

  // Tab controller
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _currentCoin = widget.coin;

    // Initialize tab controller - start on chart tab (index 0)
    _tabController = TabController(length: 2, vsync: this, initialIndex: 0);

    // Add listener to handle tab changes
    _tabController.addListener(_onTabChanged);

    // Notify lifecycle service that we're on coin detail page
    _lifecycleService.navigateToCoinDetail(widget.coin.symbol);

    // Since we start on chart tab (index 0), pause updates initially
    _subscriptionsPaused = true;
    debugPrint('⏸️ Starting on chart tab - real-time updates paused initially');
  }

  void _onTabChanged() {
    if (!_tabController.indexIsChanging) {
      final currentTab = _tabController.index;
      debugPrint('📊 Tab changed to: ${currentTab == 0 ? 'Chart' : 'Details'}');

      if (currentTab == 0) {
        // Chart tab - pause real-time updates to prevent chart reloading
        _pauseRealTimeUpdates();
        debugPrint('⏸️ Paused real-time updates for chart tab');
      } else {
        // Details tab - resume real-time updates
        _resumeRealTimeUpdates();
        debugPrint('▶️ Resumed real-time updates for details tab');
      }
    }
  }

  void _pauseRealTimeUpdates() {
    debugPrint('⏸️ Pausing real-time updates to prevent chart interference');
    _subscriptionsPaused = true;

    // Cancel existing subscriptions
    _singleCoinStreamSubscription?.cancel();
    _coinStreamSubscription?.cancel();
    _priceUpdateTimer?.cancel();
  }

  void _resumeRealTimeUpdates() {
    debugPrint('▶️ Resuming real-time updates for details tab');
    _subscriptionsPaused = false;

    // Restart real-time updates
    _startRealTimeUpdates();
  }

  void getTokenData() async {
    // Fetch TradingView symbol if needed in the future
    await fetchDexPairSymbol("solana", _currentCoin.tokenAddress ?? "");
  }

  @override
  void dispose() {
    // Stop real-time updates
    _coinStreamSubscription?.cancel();
    _singleCoinStreamSubscription?.cancel();
    _priceUpdateTimer?.cancel();

    // Cancel single coin subscription
    _supabaseCoinService.cancelSingleCoinSubscription();

    // Remove tab controller listener and dispose
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();

    // Navigate back to coins page when leaving detail page
    _lifecycleService.navigateBackFromCoinDetail();
    super.dispose();
  }

  void _startRealTimeUpdates() {
    print(
      '🔄 Starting real-time updates for coin: ${_currentCoin.symbol} (${_currentCoin.tokenAddress ?? 'No token address'})',
    );

    if (_currentCoin.tokenAddress != null) {
      // Subscribe to single coin updates using token address
      print(
        '🎯 Setting up single coin subscription for: ${_currentCoin.tokenAddress}',
      );

      _supabaseCoinService.initializeSingleCoinSubscription(
        _currentCoin.tokenAddress!,
      );

      _singleCoinStreamSubscription = _supabaseCoinService.singleCoinStream.listen((
        updatedCoin,
      ) {
        if (mounted) {
          final oldMarketCap =
              _currentCoin.currentPrice ?? 0; // currentPrice is market_cap
          final newMarketCap = updatedCoin.currentPrice ?? 0;
          final changePercent = updatedCoin.market_cap_change_percent ?? 0;

          print('💰 Single coin update for ${updatedCoin.symbol}:');
          print(
            '   Market Cap: ${CurrencyFormatter.format(oldMarketCap)} → ${CurrencyFormatter.format(newMarketCap)}',
          );
          print(
            '   Change: ${CurrencyFormatter.formatPercentage(changePercent)}',
          );

          if (!_subscriptionsPaused) {
            debugPrint('💰 Updating coin data for ${updatedCoin.symbol}');
            setState(() {
              _currentCoin = updatedCoin;
            });
          } else {
            debugPrint(
              '⏸️ Skipping coin update - subscriptions paused (chart tab active)',
            );
          }
        }
      });
    } else {
      // Use the current filtered stream from coin page instead of general stream
      print('⚠️ No token address available, using current filtered stream');

      _coinStreamSubscription = _supabaseCoinService.coinStream.listen((coins) {
        if (mounted) {
          print('📡 Received ${coins.length} coins from filtered stream');

          // Find the current coin by symbol or name
          final updatedCoin = coins.firstWhere(
            (coin) =>
                coin.symbol.toLowerCase() ==
                    _currentCoin.symbol.toLowerCase() ||
                coin.name.toLowerCase() == _currentCoin.name.toLowerCase(),
            orElse: () => _currentCoin,
          );

          if (updatedCoin != _currentCoin) {
            final oldMarketCap =
                _currentCoin.currentPrice ?? 0; // currentPrice is market_cap
            final newMarketCap = updatedCoin.currentPrice ?? 0;
            final changePercent = updatedCoin.market_cap_change_percent ?? 0;

            print('💰 Market cap update for ${updatedCoin.symbol}:');
            print(
              '   Market Cap: ${CurrencyFormatter.format(oldMarketCap)} → ${CurrencyFormatter.format(newMarketCap)}',
            );
            print(
              '   Change: ${CurrencyFormatter.formatPercentage(changePercent)}',
            );

            if (!_subscriptionsPaused) {
              debugPrint(
                '💰 Updating coin data from stream for ${updatedCoin.symbol}',
              );
              setState(() {
                _currentCoin = updatedCoin;
              });
            } else {
              debugPrint(
                '⏸️ Skipping stream update - subscriptions paused (chart tab active)',
              );
            }
          }
        }
      });
    }

    // Start a fallback timer for manual refresh (but don't rebuild the entire widget)
    _priceUpdateTimer = Timer.periodic(const Duration(seconds: 60), (timer) {
      if (mounted &&
          _lifecycleService.shouldEnableCoinDetailUpdates &&
          !_subscriptionsPaused) {
        print('⏰ Fallback timer triggered - manual refresh (price data only)');
        _refreshCoinDataOnly();
      }
    });
  }

  // Refresh only coin data without rebuilding the chart
  Future<void> _refreshCoinDataOnly() async {
    try {
      print('🔄 Refreshing coin data only (not chart) for ${_currentCoin.symbol}');

      // Try to get fresh data from Supabase
      List<CoinData> freshCoins;

      if (_currentCoin.tokenAddress != null) {
        // Search by token address first
        freshCoins = await _supabaseCoinService.searchCoins(
          _currentCoin.tokenAddress!,
        );
      } else {
        // Fallback to symbol search
        freshCoins = await _supabaseCoinService.searchCoins(
          _currentCoin.symbol,
        );
      }

      if (freshCoins.isNotEmpty) {
        final freshCoin = freshCoins.first;
        final oldMarketCap =
            _currentCoin.currentPrice ?? 0; // currentPrice is market_cap
        final newMarketCap = freshCoin.currentPrice ?? 0;
        final changePercent = freshCoin.market_cap_change_percent ?? 0;

        print('💰 Data-only refresh - Market cap for ${freshCoin.symbol}:');
        print(
          '   Market Cap: ${CurrencyFormatter.format(oldMarketCap)} → ${CurrencyFormatter.format(newMarketCap)}',
        );
        print(
          '   Change: ${CurrencyFormatter.formatPercentage(changePercent)}',
        );

        if (mounted) {
          setState(() {
            _currentCoin = freshCoin;
          });
        }
      } else {
        print('❌ No fresh data found for ${_currentCoin.symbol}');
      }
    } catch (e) {
      print('❌ Error refreshing coin data: $e');
    }
  }

  Future<void> _refreshCoinData() async {
    try {
      print('🔄 Manually refreshing coin data for ${_currentCoin.symbol}');

      // Try to get fresh data from Supabase
      List<CoinData> freshCoins;

      if (_currentCoin.tokenAddress != null) {
        // Search by token address first
        freshCoins = await _supabaseCoinService.searchCoins(
          _currentCoin.tokenAddress!,
        );
      } else {
        // Fallback to symbol search
        freshCoins = await _supabaseCoinService.searchCoins(
          _currentCoin.symbol,
        );
      }

      if (freshCoins.isNotEmpty) {
        final freshCoin = freshCoins.first;
        final oldMarketCap =
            _currentCoin.currentPrice ?? 0; // currentPrice is market_cap
        final newMarketCap = freshCoin.currentPrice ?? 0;
        final changePercent = freshCoin.market_cap_change_percent ?? 0;

        print('💰 Manual refresh - Market cap for ${freshCoin.symbol}:');
        print(
          '   Market Cap: ${CurrencyFormatter.format(oldMarketCap)} → ${CurrencyFormatter.format(newMarketCap)}',
        );
        print(
          '   Change: ${CurrencyFormatter.formatPercentage(changePercent)}',
        );

        if (mounted) {
          setState(() {
            _currentCoin = freshCoin;
          });
        }
      } else {
        print('❌ No fresh data found for ${_currentCoin.symbol}');
      }
    } catch (e) {
      print('❌ Error refreshing coin data: $e');
    }
  }

  // Example: fetch DEX pair and build symbol
  Future<String?> fetchDexPairSymbol(String chain, String tokenAddress) async {
    final url = Uri.parse(
      'https://api.dexscreener.com/token-pairs/v1/$chain/$tokenAddress',
    );
    final res = await http.get(url);
    if (res.statusCode == 200) {
      final data = jsonDecode(res.body) as List;
      if (data.isNotEmpty) {
        final pair = data[0];

        final base = pair['baseToken']['symbol'];
        final quote = pair['quoteToken']['symbol'];
        return '$base/$quote';
      }
    }
    return null;
  }

  Widget _buildDexScreenerChartFullScreen() {
    final coin = _currentCoin;
    String tokenAddress = coin.tokenAddress ?? '';

    debugPrint(
      '📊 Building chart for ${coin.symbol} with address: $tokenAddress',
    );

    return Container(
      margin: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF1E3A8A).withValues(alpha: 0.9),
            const Color(0xFF3B82F6).withValues(alpha: 0.7),
            const Color(0xFF60A5FA).withValues(alpha: 0.5),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: _buildWorkingChart(coin),
      ),
    );
  }

  Widget _buildWorkingChart(CoinData coin) {
    String tokenAddress = coin.tokenAddress ?? '';

    // Try multiple chart sources for better reliability
    if (tokenAddress.isNotEmpty) {
      return _buildDexScreenerChart(tokenAddress, coin);
    } else {
      return _buildFallbackChart(coin);
    }
  }
Widget _buildDexScreenerChart(String tokenAddress, CoinData coin) {
  debugPrint(
    '🔄 _buildDexScreenerChart called for ${coin.symbol} - subscriptions paused: $_subscriptionsPaused',
  );

  return DexScreenerChartWidget(
    tokenAddress: tokenAddress,
    coin: coin,
    key: ValueKey('chart_${coin.symbol}_${tokenAddress}'),
  );
}


  
  Widget _buildFallbackChart(CoinData coin) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF1E3A8A),
            const Color(0xFF3B82F6),
            const Color(0xFF60A5FA),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(LucideIcons.trendingUp, color: Colors.white, size: 64),
            const SizedBox(height: 24),
            Text(
              '${coin.symbol.toUpperCase()} Chart',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Current Price: \$${coin.currentPrice?.toStringAsFixed(6) ?? 'N/A'}',
              style: const TextStyle(color: Colors.white, fontSize: 18),
            ),
            const SizedBox(height: 8),
            Text(
              'Market Cap: \$${coin.marketCap?.toStringAsFixed(0) ?? 'N/A'}',
              style: const TextStyle(color: Colors.white, fontSize: 16),
            ),
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
              ),
              child: Text(
                'Chart data will be available when token address is provided',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }


  @override
  Widget build(BuildContext context) {
    debugPrint(
      '🏗️ CoinDetailPage build called - current tab: ${_tabController.index}, subscriptions paused: $_subscriptionsPaused',
    );
    final coin = _currentCoin;
    final isPositive =
        coin.market_cap_change_percent != null &&
        coin.market_cap_change_percent! >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;
    final subtitleColor = ThemeMode.system == ThemeMode.dark
        ? Colors.grey[400]
        : Colors.grey[600];

    // Check if we're on desktop
    final isDesktop = ResponsiveHelper.isDesktop(context);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: isDesktop
        ? null // No AppBar for desktop - using custom header
        : _buildMobileAppBar(context, coin, isPositive, changeColor, subtitleColor),
      body: isDesktop
        ? _buildDesktopLayout(context, coin)
        : _buildMobileLayout(context, coin),
      bottomNavigationBar: isDesktop
        ? null // No bottom navigation bar for desktop
        : SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _authService.isAuthenticated ? GradientButton(
                text: 'Start Trading',
                onPressed: _showCreateBotModal,
                icon: LucideIcons.bot,
              ) :SizedBox(), 
            ),
          ),
    );
  }

  // Mobile Layout - Preserves existing tab-based structure
  Widget _buildMobileLayout(BuildContext context, CoinData coin) {
    return Column(
      children: [
        // Tab Bar
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFF1A1A1A),
            border: Border(
              bottom: BorderSide(
                color: Colors.grey.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
          ),
          child: TabBar(
            controller: _tabController,
            indicatorColor: Colors.blue,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.grey,
            indicatorWeight: 3,
            physics: const NeverScrollableScrollPhysics(), // Disable swipe
            tabs: const [
              Tab(
                icon: Icon(LucideIcons.trendingUp, size: 20),
                text: 'Chart',
              ),
              Tab(icon: Icon(LucideIcons.info, size: 20), text: 'Details'),
            ],
          ),
        ),

        // Tab Views
        Expanded(
          child: TabBarView(
            controller: _tabController,
            physics: const NeverScrollableScrollPhysics(), // Disable swipe
            children: [
              // Chart Tab
              _buildChartTab(),

              // Details Tab
              _buildDetailsTab(),
            ],
          ),
        ),
      ],
    );
  }

  // Desktop Layout - Vertical grid structure
  Widget _buildDesktopLayout(BuildContext context, CoinData coin) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Header Section (Top Bar)
          _buildDesktopHeader(context, coin),

          // Main Content Layout (Vertical Grid)
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // First Row: TradingView Chart (Full Width)
                Container(
                  height: 500, // Fixed height for chart
                  width: double.infinity,
                  margin: const EdgeInsets.only(bottom: 24),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: _buildWorkingChart( coin)
                  ),
                ),

                // Second Row: Split into Two Columns
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Left Column: Coin Details
                    Expanded(
                      flex: 1,
                      child: Container(
                        height: 400, // Fixed height for consistency
                        margin: const EdgeInsets.only(right: 12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                          ),
                        ),
                        child: _buildCoinDetailsSection(coin),
                      ),
                    ),

                    // Right Column: AI Analysis
                    Expanded(
                      flex: 1,
                      child: Container(
                        height: 400, // Fixed height for consistency
                        margin: const EdgeInsets.only(left: 12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                          ),
                        ),
                        child: _buildAIValidationSection(coin),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }



  // Desktop Header Section with coin details and Start Bot button
  Widget _buildDesktopHeader(BuildContext context, CoinData coin) {
    final isPositive = coin.market_cap_change_percent != null && coin.market_cap_change_percent! >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Left Side: Coin Details with Price
          Expanded(
            child: Row(
              children: [
                // Coin Icon/Avatar
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    LucideIcons.coins,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),

                // Coin Name and Symbol
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        coin.displayName.isNotEmpty ? coin.displayName : coin.symbol.toUpperCase(),
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Text(
                            coin.symbol.toUpperCase(),
                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (coin.market_cap_change_percent != null) ...[
                            const SizedBox(width: 12),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: changeColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    isPositive ? LucideIcons.arrowUp : LucideIcons.arrowDown,
                                    color: changeColor,
                                    size: 12,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${coin.market_cap_change_percent!.toStringAsFixed(2)}%',
                                    style: TextStyle(
                                      color: changeColor,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),

               
              ],
            ),
          ),

          const SizedBox(width: 24),

          // Right Side: Start Bot Butto
         if  (_authService.isAuthenticated)...[
          GradientButton(
            text: 'Start Bot',
            onPressed: _showCreateBotModal,
            icon: LucideIcons.bot,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),]
        ],
      ),
    );
  }

  // TradingView Chart for Desktop/Web
  Widget _buildTradingViewChart(CoinData coin) {
  return _buildWebTradingViewChart(coin);
  }

  // Web TradingView Chart using HtmlElementView
  Widget _buildWebTradingViewChart(CoinData coin) {
    String chartUrl;

    if (coin.tokenAddress != null && coin.tokenAddress!.isNotEmpty) {
      // Use DexScreener embed URL for Solana tokens
      chartUrl = 'https://dexscreener.com/solana/${coin.tokenAddress}?embed=1&theme=dark&trades=0&info=0';
    } else {
      // Fallback to TradingView with symbol search
      final symbol = coin.symbol.toUpperCase();
      chartUrl = 'https://www.tradingview.com/widgetembed/?frameElementId=tradingview_chart&symbol=${symbol}USD&interval=1H&hidesidetoolbar=1&hidetoptoolbar=1&symboledit=1&saveimage=1&toolbarbg=f1f3f6&studies=[]&hideideas=1&theme=dark&style=1&timezone=Etc%2FUTC&studies_overrides={}&overrides={}&enabled_features=[]&disabled_features=[]&locale=en&utm_source=localhost&utm_medium=widget&utm_campaign=chart&utm_term=${symbol}USD';
    }

    // Log chart registration for debugging
    debugPrint('Registering TradingView chart for ${coin.symbol}');

    return Container(
      width: double.infinity,
      height: double.infinity,
      child: Column(
        children: [
          // Chart Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  LucideIcons.trendingUp,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '${coin.symbol.toUpperCase()} Chart',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'Live',
                    style: TextStyle(
                      color: Colors.green,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Chart Content
          Expanded(
            child: Container(
              width: double.infinity,
              height: double.infinity,
              child: _buildTradingViewChart(coin)
            ),
          ),
        ],
      ),
    );
  }

  // Web Chart using iframe
  Widget _buildWebChartIframe(String chartUrl) {
    // For web, show a placeholder with the chart URL and open button
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: const Color(0xFF0A0A0A),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            LucideIcons.trendingUp,
            size: 64,
            color: Colors.blue,
          ),
          const SizedBox(height: 24),
          Text(
            'Interactive Chart',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Chart URL: ${chartUrl.length > 50 ? chartUrl.substring(0, 50) + '...' : chartUrl}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // Open chart in new tab - for web this would use url_launcher
              debugPrint('Opening chart: $chartUrl');
              // TODO: Implement url_launcher package for cross-platform URL opening
            },
            icon: const Icon(LucideIcons.externalLink),
            label: const Text('Open Chart'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  // // Desktop TradingView Chart using WebView
  // Widget _buildDesktopTradingViewChart(CoinData coin) {
  //   String chartUrl;

  //   if (coin.tokenAddress != null && coin.tokenAddress!.isNotEmpty) {
  //     chartUrl = 'https://dexscreener.com/solana/${coin.tokenAddress}?embed=1&theme=dark&trades=0&info=0';
  //   } else {
  //     final symbol = coin.symbol.toUpperCase();
  //     chartUrl = 'https://www.tradingview.com/widgetembed/?frameElementId=tradingview_chart&symbol=${symbol}USD&interval=1H&hidesidetoolbar=1&hidetoptoolbar=1&symboledit=1&saveimage=1&toolbarbg=f1f3f6&studies=[]&hideideas=1&theme=dark&style=1&timezone=Etc%2FUTC&studies_overrides={}&overrides={}&enabled_features=[]&disabled_features=[]&locale=en&utm_source=localhost&utm_medium=widget&utm_campaign=chart&utm_term=${symbol}USD';
  //   }

  //   final webController = WebViewController()
  //     ..setJavaScriptMode(JavaScriptMode.unrestricted)
  //     ..setBackgroundColor(const Color(0xFF0A0A0A))
  //     ..setUserAgent(
  //       'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  //     )
  //     ..loadRequest(Uri.parse(chartUrl));

  //   return WebViewWidget(
  //     controller: webController,
  //   );
  // }

  // Coin Details Section for Desktop Layout
  Widget _buildCoinDetailsSection(CoinData coin) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              Icon(
                LucideIcons.info,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Coin Details',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

           Column(
              children: [
                _buildStatRow('Market Cap', coin.formattedMarketCap),
                const SizedBox(height: 16),
                _buildStatRow('24h Volume', coin.formattedVolume),
                const SizedBox(height: 16),
                _buildStatRow(
                  'Rank',
                  coin.marketCapRank != null ? '#${coin.marketCapRank}' : 'N/A',
                ),
                const SizedBox(height: 16),
                _buildStatRow(
                  'Supply',
                  coin.circulatingSupply != null
                      ? '${(coin.circulatingSupply! / 1000000).toStringAsFixed(1)}M'
                      : 'N/A',
                ),
              ],
            ),
        ],
      ),
    );
  }

  // AI Validation Section for Desktop Layout
  Widget _buildAIValidationSection(CoinData coin) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              Icon(
                LucideIcons.brain,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'AI Analysis',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // AI Prediction Widget with scroll support
          Expanded(
            child: SingleChildScrollView(
              child: AIPrediction(
                symbol: coin.symbol,
                currentPrice: coin.currentPrice ?? 0.0,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build detail rows
  Widget _buildDetailRow(String label, String value, IconData icon, {Color? valueColor}) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: valueColor ?? Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  // Mobile App Bar
  PreferredSizeWidget _buildMobileAppBar(BuildContext context, CoinData coin, bool isPositive, Color changeColor, Color? subtitleColor) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(LucideIcons.arrowLeft, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      title: Row(
        children: [
          // Coin Icon and Rank
          (coin.image != null && coin.image!.isNotEmpty)
              ? Image.network(
                  coin.image!,
                  width: 36,
                  height: 36,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Icon(
                    Icons.currency_bitcoin,
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                )
              : Center(
                  child: Text(
                    coin.symbol.substring(0, 1).toUpperCase(),
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
          SizedBox(width: 12),

          // Coin Name and Symbol
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (coin.displayName.isNotEmpty) ...[
                  Text(
                    coin.displayName,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 15,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ] else ...[
                  Text(
                    coin.displaySymbol.split(' / ')[0],
                    style: TextStyle(
                      fontWeight: FontWeight.w800,
                      fontSize: 15,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                SizedBox(height: 6),
                Row(
                  spacing: 5,
                  children: [
                    Text(
                      coin.displaySymbol,
                      style: TextStyle(
                        color: subtitleColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                    ),
                    // Coin image
                    if (coin.image != null && coin.image!.isNotEmpty) ...[
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: Image.network(coin.image!, fit: BoxFit.cover),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                if (coin.market_cap_change_percent != null)
                  Text(
                    '${isPositive ? '+' : ''}${coin.market_cap_change_percent!.toStringAsFixed(3)}%',
                    style: TextStyle(
                      color: changeColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
              ],
            ),
          ],
        ),
      );
  }

  Widget _buildChartTab() {
    return _buildDexScreenerChartFullScreen();
  }

  Widget _buildDetailsTab() {
    final coin = _currentCoin;
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // AI Prediction
            AIPrediction(
              symbol: coin.symbol,
              currentPrice: coin.currentPrice ?? 0.0,
            ),

          const SizedBox(height: 24),

            // Stats section
            Column(
              children: [
                _buildStatRow('Market Cap', coin.formattedMarketCap),
                const SizedBox(height: 16),
                _buildStatRow('24h Volume', coin.formattedVolume),
                const SizedBox(height: 16),
                _buildStatRow(
                  'Rank',
                  coin.marketCapRank != null ? '#${coin.marketCapRank}' : 'N/A',
                ),
                const SizedBox(height: 16),
                _buildStatRow(
                  'Supply',
                  coin.circulatingSupply != null
                      ? '${(coin.circulatingSupply! / 1000000).toStringAsFixed(1)}M'
                      : 'N/A',
                ),
              ],
            ),

            // Bottom padding for safe area
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  void _pauseSubscriptions() {
    debugPrint('⏸️ Pausing coin data subscriptions');
    _subscriptionsPaused = true;
    _priceUpdateTimer?.cancel();
    _coinStreamSubscription?.pause();
    _singleCoinStreamSubscription?.pause();
  }

  void _resumeSubscriptions() {
    debugPrint('▶️ Resuming coin data subscriptions');
    _subscriptionsPaused = false;
    _coinStreamSubscription?.resume();
    _singleCoinStreamSubscription?.resume();

    // Restart the timer
    _priceUpdateTimer = Timer.periodic(const Duration(seconds: 60), (timer) {
      if (mounted &&
          _lifecycleService.shouldEnableCoinDetailUpdates &&
          !_subscriptionsPaused) {
        print('⏰ Fallback timer triggered - manual refresh');
        _refreshCoinData();
      }
    });
  }

  void _showCreateBotModal() {
    debugPrint('🚀 Opening CreateBotModal from coin detail page');
    debugPrint('📊 Coin symbol: ${widget.symbol}');
    debugPrint('🆔 Coin ID: ${_currentCoin.id}');
    debugPrint('🏷️ Token Address: ${_currentCoin.tokenAddress}');
    debugPrint('💰 Full coin data: ${_currentCoin.toJson()}');

    // Pause subscriptions when modal opens
    _pauseSubscriptions();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CreateBotModal(
        coinData: _currentCoin,
        onBotCreated: () {
          debugPrint('✅ Bot created callback triggered');
          // Refresh the page or show success message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Trading bot created successfully!'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        },
      ),
    ).then((_) {
      // Resume subscriptions when modal closes
      _resumeSubscriptions();
    });
  }

  String formatNumberCompact(double? number) {
    if (number == null) return '';

    if (number >= 1e9) {
      return '${(number / 1e9).toStringAsFixed(1)}B';
    } else if (number >= 1e6) {
      return '${(number / 1e6).toStringAsFixed(1)}M';
    } else if (number >= 1e3) {
      return '${(number / 1e3).toStringAsFixed(1)}K';
    } else {
      return number.toStringAsFixed(2);
    }
  }
}

// Stateful DexScreener Chart Widget to prevent reloading on tab changes
class DexScreenerChartWidget extends StatefulWidget {
  final String tokenAddress;
  final CoinData coin;

  const DexScreenerChartWidget({
    Key? key,
    required this.tokenAddress,
    required this.coin,
  }) : super(key: key);

  @override
  State<DexScreenerChartWidget> createState() => _DexScreenerChartWidgetState();
}

class _DexScreenerChartWidgetState extends State<DexScreenerChartWidget>
    with AutomaticKeepAliveClientMixin {
  bool _isLoading = true;
  WebViewController? _webController;
  Timer? _loadingTimer;

  @override
  bool get wantKeepAlive => true; // Keep the widget alive when switching tabs

  @override
  void initState() {
    super.initState();
    _initializeChart();
  }

  @override
  void dispose() {
    _loadingTimer?.cancel();
    super.dispose();
  }

  void _initializeChart() {
    // Chart-only URL with parameters to hide DexScreener UI elements
    String chartUrl =
        'https://dexscreener.com/solana/${widget.tokenAddress}?embed=1&theme=dark&trades=0&info=0&chartType=1&chartLeftToolbar=0&chartTopToolbar=0&hotkeys=0&saveload=0&studies_overrides=%7B%7D&overrides=%7B%7D&enabled_features=%5B%5D&disabled_features=%5B%22use_localstorage_for_settings%22%2C%22right_toolbar%22%2C%22timeframes_toolbar%22%2C%22edit_buttons_in_legend%22%2C%22context_menus%22%2C%22left_toolbar%22%2C%22header_widget%22%2C%22header_symbol_search%22%2C%22symbol_search_hot_key%22%2C%22footer%22%2C%22watermark%22%2C%22branding%22%5D&hide_side_toolbar=1&hide_top_toolbar=1&hide_legend=1&hide_bottom_toolbar=1';

    debugPrint('📊 Loading chart-only view: $chartUrl');

    // Create the WebViewController for universal platforms
    _webController = WebViewController();

    // Initialize the controller
    _webController!.init(
      context: context,
      uri: Uri.parse(chartUrl),
      setState: (void Function() fn) {
        if (mounted) {
          setState(fn);
        }
      },
    );

    // Auto-hide loader after 3 seconds
    _loadingTimer = Timer(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return Container(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        children: [
          // Universal WebView
          if (_webController != null)
            WebView(
              controller: _webController!,
              key: ValueKey('CoinChart_${widget.coin.symbol}'),
            ),

          // Animated loader overlay
          if (_isLoading)
            Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Loading animation
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(40),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                        width: 2,
                      ),
                    ),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // Rotating border
                        SizedBox(
                          width: 60,
                          height: 60,
                          child: CircularProgressIndicator(
                            strokeWidth: 3,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ),
                        // Chart icon
                        Icon(
                          LucideIcons.trendingUp,
                          color: Theme.of(context).colorScheme.primary,
                          size: 32,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Loading text
                  Text(
                    'Loading ${widget.coin.symbol.toUpperCase()} Chart...',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Subtitle
                  Text(
                    'Fetching real-time data from DexScreener',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Animated dots
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(3, (index) {
                      return AnimatedContainer(
                        duration: Duration(milliseconds: 600 + (index * 200)),
                        curve: Curves.easeInOut,
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary.withValues(
                            alpha: 0.3 + (0.7 * ((DateTime.now().millisecondsSinceEpoch ~/ 500 + index) % 3) / 3),
                          ),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      );
                    }),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
