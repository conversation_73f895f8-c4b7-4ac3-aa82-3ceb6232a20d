-- SQL script to update existing default_investment_amount from 100.0 to 1.0
-- This script should be run after the initial schema creation

-- Update existing records that have the old default value of 100.0
UPDATE user_preferences 
SET default_investment_amount = 1.0 
WHERE default_investment_amount = 100.0;

-- Update the column default for future records
ALTER TABLE user_preferences 
ALTER COLUMN default_investment_amount SET DEFAULT 1.0;

-- Verify the update
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN default_investment_amount = 1.0 THEN 1 END) as records_with_new_default,
    COUNT(CASE WHEN default_investment_amount = 100.0 THEN 1 END) as records_with_old_default
FROM user_preferences;

-- Show sample of updated records
SELECT 
    user_id, 
    default_investment_amount, 
    updated_at 
FROM user_preferences 
LIMIT 5;
