import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../theme/app_theme.dart';
import '../services/wallet_service.dart';
import 'wallet_creation_page.dart';

class WalletSelectionPage extends StatefulWidget {
  const WalletSelectionPage({super.key});

  @override
  State<WalletSelectionPage> createState() => _WalletSelectionPageState();
}

class _WalletSelectionPageState extends State<WalletSelectionPage> {
  final WalletService _walletService = WalletService();

  @override
  void initState() {
    super.initState();
    _walletService.addListener(() {
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? Colors.grey[900]
          : Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Manage Wallets',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Existing wallets section
            if (_walletService.wallets.isNotEmpty) ...[
              const Text(
                'Your Wallets',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              ...(_walletService.wallets.map(
                (wallet) => _buildWalletCard(wallet),
              )),
              const SizedBox(height: 32),
            ],

            // Create new wallet section
            const Text(
              'Create New Wallet',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildCreateWalletCard(
              'Solana Wallet',
              'Generate a new Solana wallet with keys',
              LucideIcons.key,
              const Color(0xFF6934C9),
              () => _navigateToWalletCreation('solana'),
            ),
            const SizedBox(height: 12),
            _buildCreateWalletCard(
              'Paper Wallet',
              'Create an offline wallet for maximum security',
              LucideIcons.fileText,
              AppTheme.primaryColor,
              () => _navigateToWalletCreation('paper'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWalletCard(Wallet wallet) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey[850]?.withValues(alpha: 0.8)
            : Colors.white.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(16),
        border: wallet.isActive
            ? Border.all(width: 2, color: AppTheme.primaryGradient.colors.first)
            : Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            _walletService.setActiveWallet(wallet.id);
            Navigator.pop(context);
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: wallet.isActive ? AppTheme.primaryGradient : null,
                    color: wallet.isActive
                        ? null
                        : _getWalletTypeColor(
                            wallet.type,
                          ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(25),
                    border: wallet.isActive
                        ? null
                        : Border.all(
                            color: _getWalletTypeColor(wallet.type),
                            width: 2,
                          ),
                  ),
                  child: Icon(
                    _getWalletTypeIcon(wallet.type),
                    color: wallet.isActive
                        ? Colors.white
                        : _getWalletTypeColor(wallet.type),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            wallet.name,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (wallet.isActive) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                gradient: AppTheme.primaryGradient,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'ACTIVE',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${wallet.typeDisplayName} • ${wallet.balance.toStringAsFixed(4)} SOL',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                      if (wallet.address != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          wallet.displayAddress,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[500],
                            fontFamily: 'monospace',
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Icon(
                  wallet.isActive
                      ? LucideIcons.check
                      : LucideIcons.chevronRight,
                  color: wallet.isActive
                      ? AppTheme.primaryGradient.colors.first
                      : Colors.grey[400],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCreateWalletCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey[850]?.withValues(alpha: 0.8)
            : Colors.white.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color, width: 2),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(color: color, width: 2),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                Icon(LucideIcons.plus, color: color),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper methods
  Color _getWalletTypeColor(String type) {
    switch (type) {
      case 'paper':
        return AppTheme.primaryColor;
      case 'solana':
        return const Color(0xFF6934C9); // Purple for Solana wallets
      case 'external':
        return const Color(0xFF2196F3); // Blue for external wallets
      default:
        return AppTheme.primaryColor;
    }
  }

  IconData _getWalletTypeIcon(String type) {
    switch (type) {
      case 'paper':
        return LucideIcons.fileText;
      case 'solana':
        return LucideIcons.key;
      case 'external':
        return LucideIcons.link;
      default:
        return LucideIcons.wallet;
    }
  }

  void _navigateToWalletCreation(String type) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WalletCreationPage(walletType: type),
      ),
    );
  }
}
