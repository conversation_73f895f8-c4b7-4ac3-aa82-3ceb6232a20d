import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/coin_model.dart';

class Trade {
  final String id;
  final String botId;
  final String coinId;
  final String tradeType; // 'buy' or 'sell'
  final double quantity;
  final double price;
  final double totalAmount;
  final double fee;
  final Map<String, dynamic> indicatorData;
  final DateTime? executedAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  // Additional fields for UI
  final String? coinSymbol;
  final String? coinName;
  final String? coinImage;
  final double? currentPrice;
  final double? marketCap;
  final String? tokenAddress;

  Trade({
    required this.id,
    required this.botId,
    required this.coinId,
    required this.tradeType,
    required this.quantity,
    required this.price,
    required this.totalAmount,
    this.fee = 0.0,
    this.indicatorData = const {},
    this.executedAt,
    this.createdAt,
    this.updatedAt,
    this.coinSymbol,
    this.coinName,
    this.coinImage,
    this.currentPrice,
    this.marketCap,
    this.tokenAddress,
  });

  factory Trade.fromJson(Map<String, dynamic> json) {
    return Trade(
      id: json['id'] ?? '',
      botId: json['bot_id'] ?? '',
      coinId: json['coin_id'] ?? '',
      tradeType: json['trade_type'] ?? 'buy',
      quantity: (json['quantity'] ?? 0).toDouble(),
      price: (json['price'] ?? 0).toDouble(),
      totalAmount: (json['total_amount'] ?? 0).toDouble(),
      fee: (json['fee'] ?? 0).toDouble(),
      indicatorData: json['indicator_data'] ?? {},
      executedAt: json['executed_at'] != null
          ? DateTime.parse(json['executed_at'])
          : null,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
      coinSymbol: json['coin_symbol'],
      coinName: json['coin_name'],
      coinImage: json['coin_image'],
      currentPrice: json['current_price']?.toDouble(),
      marketCap: json['market_cap']?.toDouble(),
      tokenAddress: json['token_address'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bot_id': botId,
      'coin_id': coinId,
      'trade_type': tradeType,
      'quantity': quantity,
      'price': price,
      'total_amount': totalAmount,
      'fee': fee,
      'indicator_data': indicatorData,
      'executed_at': executedAt?.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Calculate profit/loss
  double get profitLoss {
    if (currentPrice == null) return 0.0;

    if (tradeType == 'buy') {
      // For buy trades, profit = (current_price - buy_price) * quantity
      return (currentPrice! - price) * quantity;
    } else {
      // For sell trades, profit = (sell_price - original_buy_price) * quantity
      // Note: This would need the original buy price, for now just return the total amount
      return totalAmount;
    }
  }

  double get profitLossPercentage {
    if (currentPrice == null || price == 0) return 0.0;

    if (tradeType == 'buy') {
      return ((currentPrice! - price) / price) * 100;
    } else {
      // For sell trades, this would need more complex calculation
      return 0.0;
    }
  }

  bool get isProfit => profitLoss > 0;
}

class TradesService {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get current user ID from Supabase auth
  String? get _currentUserId => _supabase.auth.currentUser?.id;

  /// Get all trades for the current user
  Future<List<Trade>> getUserTrades() async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        debugPrint('❌ User not authenticated');
        return [];
      }

      debugPrint('📊 Fetching trades for user: $userId');

      // First, get user's bot IDs
      final botsResponse = await _supabase
          .from('trading_bots')
          .select('id, name')
          .eq('user_id', userId);

      if (botsResponse.isEmpty) {
        debugPrint('📊 No trading bots found for user');
        return [];
      }

      final botIds = botsResponse.map((bot) => bot['id']).toList();
      debugPrint(
        '📊 Found ${botIds.length} bots for user: ${botsResponse.map((b) => b['name']).join(', ')}',
      );

      // Query trades for user's bots
      final response = await _supabase
          .from('trades')
          .select('*')
          .inFilter('bot_id', botIds)
          .order('executed_at', ascending: false)
          .limit(100); // Limit to recent 100 trades

      debugPrint('📊 Raw trades response: ${response.length} trades');

      if (response.isEmpty) {
        debugPrint('📊 No trades found for user bots');
        return [];
      }

      // Get unique coin IDs from trades
      final coinIds = response
          .map((trade) => trade['coin_id'])
          .where((coinId) => coinId != null)
          .toSet()
          .toList();

      debugPrint('📊 Fetching token data for ${coinIds.length} unique coins');

      // Fetch token data for all coins
      Map<String, Map<String, dynamic>> tokenDataMap = {};
      if (coinIds.isNotEmpty) {
        final tokensResponse = await _supabase
            .from('tokens')
            .select(
              'coin_id, symbol, name, image_url, price, market_cap, address',
            )
            .inFilter('coin_id', coinIds);

        for (final token in tokensResponse) {
          tokenDataMap[token['coin_id']] = token;
        }
        debugPrint('📊 Fetched token data for ${tokenDataMap.length} coins');
      }

      // Combine trades with token data
      final trades = response.map((tradeData) {
        final tokenData = tokenDataMap[tradeData['coin_id']];

        return Trade.fromJson({
          ...tradeData,
          'coin_symbol': tokenData?['symbol'],
          'coin_name': tokenData?['name'],
          'coin_image': tokenData?['image_url'],
          'current_price': tokenData?['price'],
          'market_cap': tokenData?['market_cap'],
          'token_address': tokenData?['address'],
        });
      }).toList();

      debugPrint('✅ Processed ${trades.length} trades');
      return trades;
    } catch (e) {
      debugPrint('❌ Error fetching trades: $e');
      return [];
    }
  }

  /// Get trades stream for real-time updates
  Stream<List<Trade>> getTradesStream() {
    final userId = _currentUserId;
    if (userId == null) {
      return Stream.empty();
    }

    // For real-time updates, we'll periodically refresh the data
    // since Supabase streams don't support complex joins
    return Stream.periodic(const Duration(seconds: 10), (_) async {
      return await getUserTrades();
    }).asyncMap((future) => future);
  }

  /// Get trades by bot ID
  Future<List<Trade>> getTradesByBot(String botId) async {
    try {
      final response = await _supabase
          .from('trades')
          .select('''
            *,
            tokens(symbol, name, image_url, current_price, market_cap)
          ''')
          .eq('bot_id', botId)
          .order('executed_at', ascending: false);

      return response.map((tradeData) {
        final tokenData = tradeData['tokens'];

        return Trade.fromJson({
          ...tradeData,
          'coin_symbol': tokenData?['symbol'],
          'coin_name': tokenData?['name'],
          'coin_image': tokenData?['image_url'],
          'current_price': tokenData?['current_price'],
          'market_cap': tokenData?['market_cap'],
        });
      }).toList();
    } catch (e) {
      debugPrint('❌ Error fetching trades for bot $botId: $e');
      return [];
    }
  }

  /// Create a new trade (for testing purposes)
  Future<Trade?> createTrade({
    required String botId,
    required String coinId,
    required String tradeType,
    required double quantity,
    required double price,
    required double totalAmount,
    double fee = 0.0,
    Map<String, dynamic> indicatorData = const {},
  }) async {
    try {
      final tradeData = {
        'bot_id': botId,
        'coin_id': coinId,
        'trade_type': tradeType,
        'quantity': quantity,
        'price': price,
        'total_amount': totalAmount,
        'fee': fee,
        'indicator_data': indicatorData,
        'executed_at': DateTime.now().toIso8601String(),
      };

      final response = await _supabase
          .from('trades')
          .insert(tradeData)
          .select()
          .single();

      return Trade.fromJson(response);
    } catch (e) {
      debugPrint('❌ Error creating trade: $e');
      return null;
    }
  }

  /// Update trades with current prices from tokens table
  Future<List<Trade>> updateTradesWithCurrentPrices(List<Trade> trades) async {
    try {
      if (trades.isEmpty) return trades;

      // Get unique coin IDs from trades
      final coinIds = trades.map((trade) => trade.coinId).toSet().toList();

      debugPrint('💰 Updating prices for ${coinIds.length} unique coins');

      // Fetch current prices from tokens table
      final tokensResponse = await _supabase
          .from('tokens')
          .select(
            'coin_id, symbol, name, image_url, price, market_cap, address',
          )
          .inFilter('coin_id', coinIds);

      // Create a map for quick lookup
      final priceMap = <String, Map<String, dynamic>>{};
      for (final token in tokensResponse) {
        priceMap[token['coin_id']] = token;
      }

      // Update trades with current prices
      final updatedTrades = trades.map((trade) {
        final tokenData = priceMap[trade.coinId];
        if (tokenData != null) {
          return Trade(
            id: trade.id,
            botId: trade.botId,
            coinId: trade.coinId,
            tradeType: trade.tradeType,
            quantity: trade.quantity,
            price: trade.price,
            totalAmount: trade.totalAmount,
            fee: trade.fee,
            indicatorData: trade.indicatorData,
            executedAt: trade.executedAt,
            createdAt: trade.createdAt,
            updatedAt: trade.updatedAt,
            coinSymbol: tokenData['symbol'],
            coinName: tokenData['name'],
            coinImage: tokenData['image_url'],
            currentPrice: tokenData['price']?.toDouble(),
            marketCap: tokenData['market_cap']?.toDouble(),
            tokenAddress: tokenData['address'],
          );
        }
        return trade;
      }).toList();

      debugPrint(
        '✅ Updated ${updatedTrades.length} trades with current prices',
      );
      return updatedTrades;
    } catch (e) {
      debugPrint('❌ Error updating trades with current prices: $e');
      return trades; // Return original trades if update fails
    }
  }

  /// Get trades with real-time price updates
  Future<List<Trade>> getUserTradesWithCurrentPrices() async {
    final trades = await getUserTrades();
    return await updateTradesWithCurrentPrices(trades);
  }

  /// Get all trades using the same method as bot service
  Future<List<Trade>> getUserTradesViaBotService() async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        debugPrint('❌ User not authenticated');
        return [];
      }

      debugPrint('📊 [VIA BOT SERVICE] Fetching trades for user: $userId');

      // Get user's bots
      final botsResponse = await _supabase
          .from('trading_bots')
          .select('id, name')
          .eq('user_id', userId);

      if (botsResponse.isEmpty) {
        debugPrint('📊 [VIA BOT SERVICE] No trading bots found for user');
        return [];
      }

      debugPrint('📊 [VIA BOT SERVICE] Found ${botsResponse.length} bots');

      // Get all trades from all bots
      final allTrades = <Map<String, dynamic>>[];
      for (final bot in botsResponse) {
        final botId = bot['id'];
        final botName = bot['name'];

        final botTrades = await _supabase
            .from('trades')
            .select('*')
            .eq('bot_id', botId)
            .order('executed_at', ascending: false);

        debugPrint(
          '📊 [VIA BOT SERVICE] Bot "$botName": ${botTrades.length} trades',
        );
        allTrades.addAll(botTrades);
      }

      // Sort all trades by execution time
      allTrades.sort((a, b) {
        final aTime = a['executed_at'] ?? a['created_at'];
        final bTime = b['executed_at'] ?? b['created_at'];
        if (aTime == null && bTime == null) return 0;
        if (aTime == null) return 1;
        if (bTime == null) return -1;
        return DateTime.parse(bTime).compareTo(DateTime.parse(aTime));
      });

      debugPrint(
        '📊 [VIA BOT SERVICE] Total trades found: ${allTrades.length}',
      );

      // Get token data for all unique coin IDs
      final coinIds = allTrades
          .map((trade) => trade['coin_id'])
          .where((coinId) => coinId != null)
          .toSet()
          .toList();

      debugPrint(
        '📊 [VIA BOT SERVICE] Fetching token data for ${coinIds.length} unique coins',
      );

      Map<String, Map<String, dynamic>> tokenDataMap = {};
      if (coinIds.isNotEmpty) {
        final tokensResponse = await _supabase
            .from('tokens')
            .select(
              'coin_id, symbol, name, image_url, price, market_cap, address',
            )
            .inFilter('coin_id', coinIds);

        for (final token in tokensResponse) {
          tokenDataMap[token['coin_id']] = token;
        }
        debugPrint(
          '📊 [VIA BOT SERVICE] Fetched token data for ${tokenDataMap.length} coins',
        );
      }

      // Convert to Trade objects with proper token data
      final trades = allTrades.map((tradeData) {
        final tokenData = tokenDataMap[tradeData['coin_id']];

        return Trade.fromJson({
          ...tradeData,
          'coin_symbol': tokenData?['symbol'] ?? tradeData['coin_id'],
          'coin_name':
              tokenData?['name'] ??
              tokenData?['symbol'] ??
              tradeData['coin_id'],
          'coin_image': tokenData?['image_url'] ?? '',
          'current_price': tokenData?['price'] ?? tradeData['price'],
          'market_cap': tokenData?['market_cap'] ?? 0.0,
          'token_address': tokenData?['address'],
        });
      }).toList();

      debugPrint('✅ [VIA BOT SERVICE] Processed ${trades.length} trades');
      return trades;
    } catch (e) {
      debugPrint('❌ [VIA BOT SERVICE] Error fetching trades: $e');
      return [];
    }
  }

  /// Get simple trades without token data for testing
  Future<List<Trade>> getUserTradesSimple() async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        debugPrint('❌ User not authenticated');
        return [];
      }

      debugPrint('📊 [SIMPLE] Fetching trades for user: $userId');

      // Get user's bot IDs
      final botsResponse = await _supabase
          .from('trading_bots')
          .select('id, name')
          .eq('user_id', userId);

      if (botsResponse.isEmpty) {
        debugPrint('📊 [SIMPLE] No trading bots found for user');
        return [];
      }

      final botIds = botsResponse.map((bot) => bot['id']).toList();
      debugPrint('📊 [SIMPLE] Found ${botIds.length} bots');

      // Get trades without token data
      final response = await _supabase
          .from('trades')
          .select('*')
          .inFilter('bot_id', botIds)
          .order('executed_at', ascending: false)
          .limit(50);

      debugPrint('📊 [SIMPLE] Found ${response.length} trades');

      // Convert to Trade objects without token data
      final trades = response.map((tradeData) {
        return Trade.fromJson({
          ...tradeData,
          'coin_symbol': tradeData['coin_id'], // Use coin_id as symbol for now
          'coin_name': tradeData['coin_id'],
          'coin_image': '',
          'current_price':
              tradeData['price'], // Use original price as current price
          'market_cap': 0.0,
        });
      }).toList();

      debugPrint('✅ [SIMPLE] Processed ${trades.length} trades');
      return trades;
    } catch (e) {
      debugPrint('❌ [SIMPLE] Error fetching trades: $e');
      return [];
    }
  }

  /// Test database connection and user authentication
  Future<void> testDatabaseConnection() async {
    try {
      debugPrint('🧪 Testing database connection...');

      // Test user authentication
      final userId = _currentUserId;
      debugPrint('👤 Current user ID: $userId');

      if (userId == null) {
        debugPrint('❌ User not authenticated');
        return;
      }

      // Test trading_bots table access
      final botsResponse = await _supabase
          .from('trading_bots')
          .select('id, name')
          .eq('user_id', userId);
      debugPrint(
        '🤖 Found ${botsResponse.length} trading bots: ${botsResponse.map((b) => b['name']).join(', ')}',
      );

      if (botsResponse.isNotEmpty) {
        final botIds = botsResponse.map((bot) => bot['id']).toList();

        // Test trades table access for user's bots
        final tradesResponse = await _supabase
            .from('trades')
            .select('id, coin_id, trade_type, quantity, price')
            .inFilter('bot_id', botIds);
        debugPrint('📊 Found ${tradesResponse.length} trades for user bots');

        // Show sample trades
        if (tradesResponse.isNotEmpty) {
          debugPrint('📊 Sample trades:');
          for (int i = 0; i < tradesResponse.length.clamp(0, 3); i++) {
            final trade = tradesResponse[i];
            debugPrint(
              '  - ${trade['trade_type']} ${trade['quantity']} ${trade['coin_id']} at ${trade['price']}',
            );
          }
        }
      }

      // Test tokens table access
      final tokensResponse = await _supabase
          .from('tokens')
          .select('coin_id, symbol, price, address')
          .limit(5);
      debugPrint('🪙 Found ${tokensResponse.length} tokens in database');

      if (tokensResponse.isNotEmpty) {
        debugPrint('🪙 Sample tokens:');
        for (int i = 0; i < tokensResponse.length.clamp(0, 2); i++) {
          final token = tokensResponse[i];
          debugPrint(
            '  - ${token['symbol']} (${token['coin_id']}) at ${token['price']}',
          );
        }
      }

      debugPrint('✅ Database connection test completed successfully');
    } catch (e) {
      debugPrint('❌ Database connection test failed: $e');
    }
  }
}
