import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../theme/app_theme.dart';
import '../services/supabase_auth_service.dart';
import '../utils/responsive_helper.dart';
import '../utils/toast_utils.dart';

class AuthBottomSheet extends StatefulWidget {
  final VoidCallback? onSuccess;

  const AuthBottomSheet({super.key, this.onSuccess});

  @override
  State<AuthBottomSheet> createState() => _AuthBottomSheetState();
}

class _AuthBottomSheetState extends State<AuthBottomSheet> with TickerProviderStateMixin {
  late TabController _tabController;
  final SupabaseAuthService _supabaseService = SupabaseAuthService();

  // Form controllers
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _otpController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // State management
  bool _isLoading = false;
  bool _isOtpSent = false;
  bool _needsName = false;
  String _currentEmail = '';

  // Current tab (0 = Login, 1 = Sign Up)
  int _currentTab = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _currentTab = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _emailController.dispose();
    _otpController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final colorScheme = Theme.of(context).colorScheme;
    final isDesktop = ResponsiveHelper.isDesktop(context);

    if (isDesktop) {
      // Desktop version - no DraggableScrollableSheet
      return Container(
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isDark ? const Color(0xFF6B7280) : const Color(0xFFE5E7EB),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.4),
              blurRadius: 30,
              offset: const Offset(0, 15),
              spreadRadius: 8,
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 15,
              offset: const Offset(0, 5),
              spreadRadius: 2,
            ),
          ],
        ),
        child: SingleChildScrollView(
          child: _buildContent(context, isDark, colorScheme),
        ),
      );
    }

    // Mobile version - with DraggableScrollableSheet
    return DraggableScrollableSheet(
      initialChildSize: 0.6,
      minChildSize: 0.4,
      maxChildSize: 0.8,
      expand: false,
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            border: Border.all(
              color: isDark ? const Color(0xFF6B7280) : const Color(0xFFE5E7EB),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 25,
                offset: const Offset(0, -10),
                spreadRadius: 2,
              ),
            ],
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: isDark
                      ? Colors.white.withValues(alpha: 0.3)
                      : Colors.grey.withValues(alpha: 0.4),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: _buildContent(context, isDark, Theme.of(context).colorScheme),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAuthForm(ColorScheme colorScheme) {

    return SingleChildScrollView(
      padding: EdgeInsets.only(
        left: 24,
        right: 24,
        bottom: MediaQuery.of(context).viewInsets.bottom + 24,
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Tab bar
            if (!_isOtpSent) ...[
              Container(
                decoration: BoxDecoration(
                  color: colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: TabBar(
                  controller: _tabController,
                  onTap: (index) {
                    setState(() {
                      _currentTab = index;
                    });
                  },
                  indicator: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: AppTheme.primaryGradient,
                  ),
                  indicatorSize: TabBarIndicatorSize.tab,
                  dividerColor: Colors.transparent,
                  labelColor: Colors.white,
                  unselectedLabelColor: colorScheme.onSurface.withValues(alpha: 0.6),
                  tabs: const [
                    Tab(text: 'Login'),
                    Tab(text: 'Sign Up'),
                  ],
                ),
              ),
              const SizedBox(height: 32),
            ],

            // Email input or OTP input
            if (!_isOtpSent) ...[
              Text(
                _currentTab == 0 ? 'Welcome back!' : 'Create your account',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Enter your email to ${_currentTab == 0 ? 'sign in' : 'get started'}',
                style: TextStyle(
                  fontSize: 14,
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
              const SizedBox(height: 24),
              TextFormField(
                controller: _emailController,
                keyboardType: TextInputType.emailAddress,
                decoration: InputDecoration(
                  labelText: 'Email Address',
                  hintText: 'Enter your email',
                  prefixIcon: const Icon(LucideIcons.mail),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your email';
                  }
                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),
              _buildSubmitButton('Continue'),
            ] else ...[
              // OTP verification
              Text(
                'Check your email',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'We sent a verification code to $_currentEmail',
                style: TextStyle(
                  fontSize: 14,
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),


              const SizedBox(height: 24),
              TextFormField(
                controller: _otpController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Verification Code',
                  hintText: 'Enter 6-digit code',
                  prefixIcon: const Icon(LucideIcons.shield),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter the verification code';
                  }
                  if (value.length != 6) {
                    return 'Code must be 6 digits';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: _isLoading ? null : _resendOtp,
                child: Text('Resend code'),
              ),
              const SizedBox(height: 24),
              _buildSubmitButton('Verify'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNameForm(ColorScheme colorScheme) {

    return SingleChildScrollView(
      padding: EdgeInsets.only(
        left: 24,
        right: 24,
        bottom: MediaQuery.of(context).viewInsets.bottom + 24,
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'What\'s your name?',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Help us personalize your experience',
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            const SizedBox(height: 24),
            TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: 'First Name',
                hintText: 'Enter your first name',
                prefixIcon: const Icon(LucideIcons.user),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter your first name';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            _buildSubmitButton('Complete Setup'),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton(String text) {
    return Container(
      width: double.infinity,
      height: 50,
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleSubmit,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                text,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (_needsName) {
        await _completeProfile();
      } else if (_isOtpSent) {
        await _verifyOtp();
      } else {
        await _sendOtp();
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(context, e.toString());
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _sendOtp() async {
    _currentEmail = _emailController.text.trim();

    try {
      debugPrint('📧 Sending Supabase OTP to: $_currentEmail');

      // Use Supabase Auth OTP
      await Supabase.instance.client.auth.signInWithOtp(
        email: _currentEmail,
        emailRedirectTo: null, // We don't want email redirect, just OTP
      );

      setState(() {
        _isOtpSent = true;
      });

      if (mounted) {
        ToastUtils.showSuccess(context, 'Verification code sent to $_currentEmail\n\nCheck your email for the 6-digit code.');
      }

      debugPrint('✅ Supabase OTP sent successfully');
    } catch (e) {
      debugPrint('❌ Supabase OTP error: $e');

      if (mounted) {
        ToastUtils.showError(context, 'Failed to send verification code: ${e.toString()}');
      }
    }
  }

  Future<void> _verifyOtp() async {
    try {
      debugPrint('🔐 Verifying Supabase OTP: ${_otpController.text}');

      // Verify OTP with Supabase Auth
      final response = await Supabase.instance.client.auth.verifyOTP(
        type: OtpType.email,
        token: _otpController.text.trim(),
        email: _currentEmail,
      );

      if (response.user != null) {
        debugPrint('✅ Supabase OTP verification successful');
        debugPrint('User ID: ${response.user!.id}');
        debugPrint('User Email: ${response.user!.email}');

        // Check if user needs to complete profile
        final userMetadata = response.user!.userMetadata;
        final hasName = userMetadata?['full_name'] != null ||
                       userMetadata?['name'] != null;

        if (!hasName) {
          setState(() {
            _needsName = true;
          });
        } else {
          await _completeAuthentication();
        }
      } else {
        throw Exception('Authentication failed - no user returned');
      }
    } catch (e) {
      debugPrint('❌ Supabase OTP verification error: $e');

      String errorMessage = 'Invalid verification code';
      if (e.toString().contains('expired')) {
        errorMessage = 'Verification code has expired';
      } else if (e.toString().contains('invalid')) {
        errorMessage = 'Invalid verification code';
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ $errorMessage'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _completeProfile() async {
    try {
      debugPrint('👤 Updating user profile with name: ${_nameController.text.trim()}');

      // Update user metadata in Supabase Auth
      await Supabase.instance.client.auth.updateUser(
        UserAttributes(
          data: {
            'full_name': _nameController.text.trim(),
            'name': _nameController.text.trim(),
          },
        ),
      );

      debugPrint('✅ User profile updated successfully');
      await _completeAuthentication();
    } catch (e) {
      debugPrint('❌ Profile update error: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Failed to update profile: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _resendOtp() async {
    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint('🔄 Resending Supabase OTP to: $_currentEmail');

      // Resend OTP using Supabase Auth
      await Supabase.instance.client.auth.signInWithOtp(
        email: _currentEmail,
        emailRedirectTo: null,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ New verification code sent to $_currentEmail'),
            backgroundColor: Colors.green,
          ),
        );
      }

      debugPrint('✅ Supabase OTP resent successfully');
    } catch (e) {
      debugPrint('❌ Resend OTP error: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Failed to resend code: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _completeAuthentication() async {
    try {
      debugPrint('🎉 Completing Supabase authentication');

      // Get current user from Supabase Auth
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        throw Exception('No authenticated user found');
      }

      // Create user profile data
      final userData = {
        'id': user.id,
        'email': user.email!,
        'full_name': user.userMetadata?['full_name'] ?? user.userMetadata?['name'],
        'avatar_url': user.userMetadata?['avatar_url'],
        'created_at': user.createdAt,
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Save to profiles table
      await _supabaseService.upsertUserProfile(userData);

      debugPrint('✅ User profile saved to database');
      debugPrint('User ID: ${user.id}');
      debugPrint('Email: ${user.email}');
      debugPrint('Name: ${userData['full_name']}');

      if (mounted) {
        Navigator.pop(context);

        // Add a small delay to ensure dialog is closed before calling onSuccess
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            widget.onSuccess?.call();
            ToastUtils.showSuccess(context, '🎉 Welcome to DexTrip!');
          }
        });
      }
    } catch (e) {
      debugPrint('❌ Error completing authentication: $e');
      if (mounted) {
        ToastUtils.showError(context, 'Error saving user data: ${e.toString()}');
      }
    }
  }

  Widget _buildContent(BuildContext context, bool isDark, ColorScheme colorScheme) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Header with close button (desktop only)
        if (ResponsiveHelper.isDesktop(context))
          Padding(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Welcome to DexTrip',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(
                    LucideIcons.x,
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),

        // Content
        Padding(
          padding: const EdgeInsets.all(24),
          child: _needsName ? _buildNameForm(colorScheme) : _buildAuthForm(colorScheme),
        ),
      ],
    );
  }
}
