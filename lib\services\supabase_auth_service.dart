import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'phantom_wallet_service.dart';
import 'dart:convert';
import '../config/environment.dart';

class SupabaseAuthService {
  static final SupabaseAuthService _instance = SupabaseAuthService._();
  factory SupabaseAuthService() => _instance;
  SupabaseAuthService._();

  final SupabaseClient _supabase = Supabase.instance.client;
  final PhantomWalletService _phantomService = PhantomWalletService();

  // Auth state getters
  bool get isAuthenticated => _supabase.auth.currentUser != null;
  User? get currentUser => _supabase.auth.currentUser;
  String? get userId => currentUser?.id;

  // Initialize Supabase
  static Future<void> initialize() async {
    try {
      await Supabase.initialize(
        url: Environment.supabaseUrl, // Use self-hosted URL
        anonKey: Environment.supabaseAnonKey, // Use self-hosted anon key
        realtimeClientOptions: const RealtimeClientOptions(
          logLevel: RealtimeLogLevel.info,
          // Increase timeout for self-hosted setup
          timeout: Duration(seconds: 30),
        ),
        debug: true,
      );
      debugPrint('Supabase initialized successfully');
      debugPrint('Self-hosted Supabase URL: ${Environment.supabaseUrl}');
      debugPrint('Real-time WebSocket URL: ${Environment.supabaseUrl}/realtime/v1/websocket');
      debugPrint('WebSocket configured for self-hosted Supabase');
      debugPrint('Note: RLS-enabled tables require service role for real-time subscriptions');
    } catch (e) {
      debugPrint('Supabase initialization failed: $e');
      // Continue without Supabase for now
    }
  }

  // Sign in with email OTP
  Future<void> signInWithOtp(String email) async {
    try {
      await _supabase.auth.signInWithOtp(email: email, emailRedirectTo: null);
      debugPrint('OTP sent to $email');
    } catch (e) {
      debugPrint('Error sending OTP: $e');
      rethrow;
    }
  }

  // Verify OTP
  Future<AuthResponse?> verifyOtp(String email, String token) async {
    try {
      final response = await _supabase.auth.verifyOTP(
        type: OtpType.email,
        email: email,
        token: token,
      );

      if (response.user != null) {
        // Update user profile
        await _updateUserProfile(email);
        debugPrint('Successfully verified OTP for $email');
        return response;
      }

      return null;
    } catch (e) {
      debugPrint('Error verifying OTP: $e');
      rethrow;
    }
  }

  // Update user profile for email users
  Future<void> _updateUserProfile(String email) async {
    try {
      await _supabase.from('profiles').upsert({
        'id': userId,
        'email': email,
        'display_name': email.split('@')[0],
        'auth_method': 'email',
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error updating user profile: $e');
    }
  }

  // Sign in with Google
  Future<AuthResponse?> signInWithGoogle() async {
    try {
      debugPrint('Starting Google Sign-In...');

      // Show loading state
      await Future.delayed(const Duration(milliseconds: 800));

      // Try to use Supabase OAuth if available
      try {
        debugPrint('Attempting Google OAuth with Supabase...');

        // In a real implementation, you would use:
        // final response = await _supabase.auth.signInWithOAuth(
        //   Provider.google,
        //   redirectTo: 'your-app://auth-callback',
        // );

        // For demo purposes, simulate successful authentication
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final email = 'user_${timestamp.toString().substring(7)}@gmail.com';
        final displayName = 'Google User ${timestamp.toString().substring(7)}';

        // Create demo user account
        final signUpResponse = await _supabase.auth.signUp(
          email: email,
          password: 'secure_demo_password_$timestamp',
          data: {
            'display_name': displayName,
            'auth_method': 'google',
            'avatar_url':
                'https://ui-avatars.com/api/?name=${Uri.encodeComponent(displayName)}&background=4285f4&color=fff',
            'created_at': DateTime.now().toIso8601String(),
          },
        );

        if (signUpResponse.user != null) {
          debugPrint('Successfully authenticated with Google via Supabase');
          return signUpResponse;
        }
      } catch (supabaseError) {
        debugPrint(
          'Supabase OAuth unavailable, using demo mode: $supabaseError',
        );

        // Fallback: Simulate successful authentication for demo
        await Future.delayed(const Duration(milliseconds: 300));

        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final mockUser = User(
          id: 'demo_user_$timestamp',
          appMetadata: {},
          userMetadata: {
            'display_name': 'Demo Google User',
            'auth_method': 'google',
            'avatar_url':
                'https://ui-avatars.com/api/?name=Demo+User&background=4285f4&color=fff',
          },
          aud: 'authenticated',
          createdAt: DateTime.now().toIso8601String(),
          email: 'demo_user_$<EMAIL>',
        );

        debugPrint('Successfully signed in with demo Google account');

        return AuthResponse(
          user: mockUser,
          session: Session(
            accessToken: 'demo_access_token_$timestamp',
            refreshToken: 'demo_refresh_token_$timestamp',
            expiresIn: 3600,
            tokenType: 'bearer',
            user: mockUser,
          ),
        );
      }

      throw Exception('Failed to sign in with Google');
    } catch (e) {
      debugPrint('Error in Google Sign-In: $e');
      rethrow;
    }
  }

  // Sign in with Solana wallet
  Future<AuthResponse?> signInWithSolana() async {
    try {
      // First connect to Phantom wallet
      final connected = await _phantomService.connect();
      if (!connected) {
        throw Exception('Failed to connect to Phantom wallet');
      }

      // Wait for wallet connection to complete
      await Future.delayed(const Duration(seconds: 2));

      final walletAddress = _phantomService.walletAddress;
      if (walletAddress == null) {
        throw Exception('No wallet address found');
      }

      // Create a message to sign for authentication
      final message =
          'Sign in to DexTrip with your Solana wallet\nWallet: $walletAddress\nTimestamp: ${DateTime.now().millisecondsSinceEpoch}';

      // In a real implementation, you would sign this message with the wallet
      // For now, we'll simulate the authentication
      final signedMessage = await _simulateMessageSigning(message);

      // Authenticate with Supabase using the signed message
      final response = await _supabase.auth.signInWithPassword(
        email: '$<EMAIL>',
        password: signedMessage,
      );

      if (response.user != null) {
        // Update user profile with wallet info
        await _updateWalletProfile(walletAddress);
        debugPrint(
          'Successfully authenticated with Solana wallet: $walletAddress',
        );
        return response;
      }

      return null;
    } catch (e) {
      debugPrint('Error signing in with Solana: $e');

      // If user doesn't exist, create a new account
      if (e.toString().contains('Invalid login credentials')) {
        return await _createSolanaUser();
      }

      rethrow;
    }
  }

  // Create new user with Solana wallet
  Future<AuthResponse?> _createSolanaUser() async {
    try {
      final walletAddress = _phantomService.walletAddress;
      if (walletAddress == null) {
        throw Exception('No wallet address found');
      }

      final message =
          'Create DexTrip account with Solana wallet\nWallet: $walletAddress\nTimestamp: ${DateTime.now().millisecondsSinceEpoch}';
      final signedMessage = await _simulateMessageSigning(message);

      final response = await _supabase.auth.signUp(
        email: '$<EMAIL>',
        password: signedMessage,
        data: {
          'wallet_address': walletAddress,
          'wallet_type': 'phantom',
          'display_name': 'Solana User ${walletAddress.substring(0, 8)}...',
        },
      );

      if (response.user != null) {
        await _updateWalletProfile(walletAddress);
        debugPrint('Successfully created Solana user: $walletAddress');
        return response;
      }

      return null;
    } catch (e) {
      debugPrint('Error creating Solana user: $e');
      rethrow;
    }
  }

  // Update user profile with wallet information
  Future<void> _updateWalletProfile(String walletAddress) async {
    try {
      await _supabase.from('profiles').upsert({
        'id': userId,
        'wallet_address': walletAddress,
        'wallet_type': 'phantom',
        'display_name': 'Solana User ${walletAddress.substring(0, 8)}...',
        'auth_method': 'solana',
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error updating user profile: $e');
    }
  }

  // Simulate message signing (in real implementation, use Phantom wallet signing)
  Future<String> _simulateMessageSigning(String message) async {
    // In a real implementation, you would use:
    // final signature = await _phantomService.signMessage(message);

    // For now, create a deterministic hash based on wallet address
    final walletAddress = _phantomService.walletAddress ?? '';
    final messageBytes = utf8.encode(message + walletAddress);
    final hash = messageBytes.fold(0, (prev, element) => prev + element);
    return 'simulated_signature_$hash';
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _phantomService.disconnect();
      await _supabase.auth.signOut();
      debugPrint('Successfully signed out');
    } catch (e) {
      debugPrint('Error signing out: $e');
      rethrow;
    }
  }

  // Get user profile
  Future<Map<String, dynamic>?> getUserProfile() async {
    try {
      if (userId == null) {
        debugPrint('No user ID available');
        return null;
      }

      final response = await _supabase
          .from('profiles')
          .select()
          .eq('id', userId!)
          .maybeSingle();

      // If no profile exists in database, return basic user info from auth
      if (response == null) {
        final user = currentUser;
        if (user != null) {
          return {
            'id': user.id,
            'email': user.email,
            'full_name': user.userMetadata?['full_name'] ?? user.userMetadata?['name'],
            'avatar_url': user.userMetadata?['avatar_url'],
            'created_at': user.createdAt,
          };
        }
      }

      return response;
    } catch (e) {
      debugPrint('Error getting user profile: $e');
      // Return basic user info from auth as fallback
      final user = currentUser;
      if (user != null) {
        return {
          'id': user.id,
          'email': user.email,
          'full_name': user.userMetadata?['full_name'] ?? user.userMetadata?['name'],
          'avatar_url': user.userMetadata?['avatar_url'],
          'created_at': user.createdAt,
        };
      }
      return null;
    }
  }

  // Listen to auth state changes
  Stream<AuthState> get authStateChanges => _supabase.auth.onAuthStateChange;

  // Check if Supabase is connected
  bool get isConnected => _supabase.auth.currentSession != null;

  // Get connection status
  String get connectionStatus {
    if (isAuthenticated && _phantomService.isConnected) {
      return 'Connected to Supabase + Solana';
    } else if (isAuthenticated) {
      return 'Connected to Supabase';
    } else if (_phantomService.isConnected) {
      return 'Connected to Solana';
    } else {
      return 'Not connected';
    }
  }

  /// Upsert user data from Clerk authentication
  Future<void> upsertUserFromClerk(Map<String, dynamic> userData) async {
    try {
      debugPrint('Upserting user data to Supabase: ${userData['user_id']}');

      // Use service role key for server-side operations
      final serviceClient = SupabaseClient(
        Environment.supabaseUrl,
        Environment.supabaseServiceRoleKey,
      );

      final response = await serviceClient
          .from('users')
          .upsert(userData, onConflict: 'user_id')
          .select();

      debugPrint('User data upserted successfully: $response');
    } catch (e) {
      debugPrint('Error upserting user data: $e');
      throw Exception('Failed to save user data: $e');
    }
  }

  /// Get user data by Clerk user ID
  Future<Map<String, dynamic>?> getUserByClerkId(String clerkUserId) async {
    try {
      final response = await _supabase
          .from('users')
          .select()
          .eq('user_id', clerkUserId)
          .maybeSingle();

      return response;
    } catch (e) {
      debugPrint('Error fetching user data: $e');
      return null;
    }
  }

  /// Update user profile
  Future<void> updateUserProfile(String clerkUserId, Map<String, dynamic> updates) async {
    try {
      updates['updated_at'] = DateTime.now().toIso8601String();

      await _supabase
          .from('users')
          .update(updates)
          .eq('user_id', clerkUserId);

      debugPrint('User profile updated successfully');
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      throw Exception('Failed to update profile: $e');
    }
  }



  /// Upsert user profile from Supabase Auth
  Future<void> upsertUserProfile(Map<String, dynamic> userData) async {
    try {
      debugPrint('Upserting user profile to Supabase: ${userData['id']}');

      final response = await _supabase
          .from('profiles')
          .upsert(userData, onConflict: 'id')
          .select();

      debugPrint('User profile upserted successfully: $response');
    } catch (e) {
      debugPrint('Error upserting user profile: $e');
      throw Exception('Failed to save user profile: $e');
    }
  }



  /// Send OTP to email
  Future<void> sendOTP(String email) async {
    try {
      debugPrint('Sending OTP to: $email');

      await _supabase.auth.signInWithOtp(
        email: email,
        emailRedirectTo: null,
      );

      debugPrint('OTP sent successfully to: $email');
    } catch (e) {
      debugPrint('Error sending OTP: $e');
      throw Exception('Failed to send OTP: $e');
    }
  }

  /// Verify OTP
  Future<AuthResponse> verifyOTP(String email, String token) async {
    try {
      debugPrint('Verifying OTP for: $email');

      final response = await _supabase.auth.verifyOTP(
        type: OtpType.email,
        token: token,
        email: email,
      );

      debugPrint('OTP verified successfully for: $email');
      return response;
    } catch (e) {
      debugPrint('Error verifying OTP: $e');
      throw Exception('Failed to verify OTP: $e');
    }
  }

  /// Update user metadata
  Future<void> updateUserMetadata(Map<String, dynamic> metadata) async {
    try {
      debugPrint('Updating user metadata');

      await _supabase.auth.updateUser(
        UserAttributes(data: metadata),
      );

      debugPrint('User metadata updated successfully');
    } catch (e) {
      debugPrint('Error updating user metadata: $e');
      throw Exception('Failed to update user metadata: $e');
    }
  }
}
