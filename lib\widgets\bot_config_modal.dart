import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../theme/app_theme.dart';
import '../models/trading_bot_model.dart' as old_model;
import '../services/trading_bot_service.dart';
import '../services/wallet_service.dart';
import '../services/user_preferences_service.dart';
import '../models/trading_bot.dart';
import '../models/coin_model.dart';
import '../models/bot_default_preferences.dart';
import '../utils/toast_utils.dart';

enum BotConfigSection {
  main,
  strategySelection,
  solPerTradeSettings,
  maxTradesSettings,
  riskSettings,
  timezoneSettings,
  tradingHoursSettings,
  indicatorSettings,
  addNewIndicator,
}

class BotConfigModal extends StatefulWidget {
  final old_model.TradingBot bot;
  final bool isCreating;
  final bool isEditMode;
  final VoidCallback? onBotCreated;
  final VoidCallback? onBotUpdated;
  final CoinData? coinData;

  const BotConfigModal({
    super.key,
    required this.bot,
    this.isCreating = false,
    this.isEditMode = false,
    this.onBotCreated,
    this.onBotUpdated,
    this.coinData,
  });

  @override
  State<BotConfigModal> createState() => _BotConfigModalState();
}

class CreateBotModal extends StatefulWidget {
  final CoinData coinData;
  final VoidCallback onBotCreated;

  const CreateBotModal({
    super.key,
    required this.coinData,
    required this.onBotCreated,
  });

  @override
  State<CreateBotModal> createState() => _CreateBotModalState();
}

class _BotConfigModalState extends State<BotConfigModal> {
  BotConfigSection _currentSection = BotConfigSection.main;
  String _selectedStrategy = 'DCA';
  String _selectedIndicator = '';
  bool _isCreating = false;
  final ScrollController _scrollController = ScrollController();
  double _savedScrollPosition = 0.0;

  // Wallet selection
  final WalletService _walletService = WalletService();
  final UserPreferencesService _preferencesService = UserPreferencesService();
  List<Wallet> _userWallets = [];
  Wallet? _selectedWallet;
  bool _isLoadingWallets = true;

  // Indicator settings - Default to OFF
  bool _useRSI = false;
  bool _useMACD = false;
  bool _useBollingerBands = false;
  bool _useEMA = false;
  bool _useVolumeProfile = false;

  // RSI Settings
  int _rsiPeriod = 14;
  int _rsiOverbought = 70;
  int _rsiOversold = 30;

  // Risk settings
  double _stopLossPercentage = 5.0;
  double _takeProfitPercentage = 15.0;

  // All Time High settings (in K values, stored as actual value in DB)
  double? _allTimeHighValue; // Actual value for DB (e.g., 150000)
  final TextEditingController _athController = TextEditingController();

  // Market Cap settings (in K values, stored as actual value in DB)
  double? _minMarketCapValue; // Actual value for DB (e.g., 100000)
  double? _maxMarketCapValue; // Actual value for DB (e.g., 1000000)
  final TextEditingController _minMarketCapController = TextEditingController();
  final TextEditingController _maxMarketCapController = TextEditingController();

  // EMA Settings
  int _emaFastPeriod = 12;
  int _emaSlowPeriod = 26;

  // KST Settings
  bool _useKST = false;
  List<int> _kstRocPeriods = [10, 15, 20, 30];
  List<int> _kstSmaPeriods = [10, 10, 10, 15];
  int _kstSignalPeriod = 9;

  // Risk level settings (multi-select)
  Set<String> _selectedRiskLevels = {'Good'}; // Good, Warning, Danger

  // Trading settings
  double _solPerTrade = 0.1;
  int _maxDailyTrades = 50;

  // Timezone
  String _selectedTimezone = 'UTC';

  // Trading hours
  bool _trading24_7 = true;
  String _tradingStartTime = '09:00';
  String _tradingEndTime = '17:00';
  bool _marketHoursOnly = false;

  @override
  void initState() {
    super.initState();
    _selectedStrategy = widget.bot.strategy;

    // If in edit mode, prefill all data from the bot
    if (widget.isEditMode) {
      _prefillBotData();
    } else {
      // Load default preferences for new bot creation
      _loadDefaultPreferences();
    }

    _loadUserWallets();
  }

  Future<void> _loadDefaultPreferences() async {
    try {
      debugPrint('📊 Loading default bot preferences...');
      final preferences = await _preferencesService.getBotDefaultPreferences();

      if (preferences != null) {
        setState(() {
          // Apply default basic settings
          _selectedStrategy = preferences.defaultStrategy;
          _solPerTrade = preferences.defaultInvestmentAmount;
          _stopLossPercentage = preferences.defaultStopLoss;
          _takeProfitPercentage = preferences.defaultTakeProfit;
          _trading24_7 = preferences.defaultTrading24_7;
          _tradingStartTime = preferences.defaultTradingStartTime;
          _tradingEndTime = preferences.defaultTradingEndTime;
          _maxDailyTrades = preferences.defaultMaxDailyTrades;

          // Apply default market cap settings
          if (preferences.defaultAllTimeHighValue != null) {
            _allTimeHighValue = preferences.defaultAllTimeHighValue;
            _athController.text = (preferences.defaultAllTimeHighValue! / 1000)
                .toString();
          }
          if (preferences.defaultMinMarketCap != null) {
            _minMarketCapValue = preferences.defaultMinMarketCap;
            _minMarketCapController.text =
                (preferences.defaultMinMarketCap! / 1000).toString();
          }
          if (preferences.defaultMaxMarketCap != null) {
            _maxMarketCapValue = preferences.defaultMaxMarketCap;
            _maxMarketCapController.text =
                (preferences.defaultMaxMarketCap! / 1000).toString();
          }

          // Apply default indicator settings
          _useRSI = preferences.defaultUseRsi;
          _rsiPeriod = preferences.defaultRsiPeriod;
          _rsiOverbought = preferences.defaultRsiOverbought;
          _rsiOversold = preferences.defaultRsiOversold;

          _useMACD = preferences.defaultUseMacd;
          _useBollingerBands = preferences.defaultUseBollinger;

          _useEMA = preferences.defaultUseEma;
          _emaFastPeriod = preferences.defaultEmaFastPeriod;
          _emaSlowPeriod = preferences.defaultEmaSlowPeriod;

          _useKST = preferences.defaultUseKst;
          _kstRocPeriods = List<int>.from(preferences.defaultKstRocPeriods);
          _kstSmaPeriods = List<int>.from(preferences.defaultKstSmaPeriods);
          _kstSignalPeriod = preferences.defaultKstSignalPeriod;

          _useVolumeProfile = preferences.defaultUseVolumeProfile;
        });

        debugPrint('✅ Default preferences loaded successfully');
      } else {
        debugPrint('📊 No default preferences found, using hardcoded defaults');
      }
    } catch (e) {
      debugPrint('❌ Error loading default preferences: $e');
    }
  }

  void _prefillBotData() {
    // Prefill basic trading settings
    _solPerTrade = widget.bot.solPerTrade;

    // Load bot data from database to get all fields
    _loadBotDataFromDatabase();
  }

  Future<void> _loadBotDataFromDatabase() async {
    try {
      debugPrint('🔄 Loading bot data from database for editing...');

      final botsData = await TradingBotService().getUserTradingBots();
      final botData = botsData.firstWhere(
        (b) => b['id'] == widget.bot.id,
        orElse: () => <String, dynamic>{},
      );

      if (botData.isNotEmpty) {
        setState(() {
          // Prefill all form fields with database values
          _selectedStrategy = botData['strategy'] ?? 'DCA';
          _solPerTrade = (botData['investment_amount'] ?? 100.0) / 100.0;
          _stopLossPercentage =
              (botData['stop_loss'] as num?)?.toDouble() ?? 5.0;
          _takeProfitPercentage =
              (botData['take_profit'] as num?)?.toDouble() ?? 15.0;
          _maxDailyTrades = botData['max_daily_trades'] ?? 50;
          _trading24_7 = botData['trading_24_7'] ?? true;
          _tradingStartTime = botData['trading_start_time'] ?? '09:00';
          _tradingEndTime = botData['trading_end_time'] ?? '17:00';

          // Prefill All Time High value
          if (botData['all_time_high_value'] != null) {
            _allTimeHighValue = (botData['all_time_high_value'] as num)
                .toDouble();
            // Convert back to K value for display (e.g., 150000 -> 150)
            final kValue = (_allTimeHighValue! / 1000).toStringAsFixed(0);
            _athController.text = kValue;
          }

          // Prefill Market Cap values
          if (botData['min_market_cap'] != null) {
            _minMarketCapValue = (botData['min_market_cap'] as num).toDouble();
            // Convert back to K value for display (e.g., 100000 -> 100)
            final kValue = (_minMarketCapValue! / 1000).toStringAsFixed(0);
            _minMarketCapController.text = kValue;
          }

          if (botData['max_market_cap'] != null) {
            _maxMarketCapValue = (botData['max_market_cap'] as num).toDouble();
            // Convert back to K value for display (e.g., 1000000 -> 1000)
            final kValue = (_maxMarketCapValue! / 1000).toStringAsFixed(0);
            _maxMarketCapController.text = kValue;
          }

          // Prefill Risk Levels
          if (botData['risk_levels'] != null) {
            _selectedRiskLevels = Set<String>.from(
              botData['risk_levels'] as List,
            );
          }

          // Prefill indicators
          _prefillIndicators(botData['indicators']);
        });

        debugPrint('✅ Bot data prefilled successfully');
        debugPrint('📊 Strategy: $_selectedStrategy');
        debugPrint('💰 SOL per trade: $_solPerTrade');
        debugPrint('📈 Stop Loss: $_stopLossPercentage%');
        debugPrint('📈 Take Profit: $_takeProfitPercentage%');
        debugPrint('🎯 ATH Value: $_allTimeHighValue');
        debugPrint('🛡️ Risk Levels: $_selectedRiskLevels');
      }
    } catch (e) {
      debugPrint('❌ Error loading bot data: $e');
    }
  }

  void _prefillIndicators(dynamic indicatorsData) {
    if (indicatorsData == null) return;

    try {
      final indicators = indicatorsData as List;
      debugPrint('📊 Prefilling ${indicators.length} indicators');

      // Reset indicator flags
      _useRSI = false;
      _useMACD = false;
      _useBollingerBands = false;
      _useEMA = false;
      _useVolumeProfile = false;

      for (final indicator in indicators) {
        final type = indicator['type'] as String?;
        final isEnabled = indicator['isEnabled'] as bool? ?? true;

        if (!isEnabled) continue;

        switch (type) {
          case 'rsi':
            _useRSI = true;
            final params = indicator['parameters'] as Map<String, dynamic>?;
            if (params != null) {
              _rsiPeriod = params['period'] ?? 14;
            }
            _rsiOverbought =
                (indicator['sellThreshold'] as num?)?.toInt() ?? 70;
            _rsiOversold = (indicator['buyThreshold'] as num?)?.toInt() ?? 30;
            debugPrint(
              '📊 RSI enabled: period=$_rsiPeriod, overbought=$_rsiOverbought, oversold=$_rsiOversold',
            );
            break;

          case 'macd':
            _useMACD = true;
            debugPrint('📊 MACD enabled');
            break;

          case 'bollinger':
            _useBollingerBands = true;
            debugPrint('📊 Bollinger Bands enabled');
            break;

          case 'ema':
            _useEMA = true;
            final params = indicator['parameters'] as Map<String, dynamic>?;
            if (params != null) {
              _emaFastPeriod = params['fastPeriod'] ?? 12;
              _emaSlowPeriod = params['slowPeriod'] ?? 26;
            }
            debugPrint(
              '📊 EMA enabled with fast: $_emaFastPeriod, slow: $_emaSlowPeriod',
            );
            break;

          case 'volume':
            _useVolumeProfile = true;
            debugPrint('📊 Volume Profile enabled');
            break;

          case 'kst':
            _useKST = true;
            final params = indicator['parameters'] as Map<String, dynamic>?;
            if (params != null) {
              _kstRocPeriods = List<int>.from(
                params['rocPeriods'] ?? [10, 15, 20, 30],
              );
              _kstSmaPeriods = List<int>.from(
                params['smaPeriods'] ?? [10, 10, 10, 15],
              );
              _kstSignalPeriod = params['signalPeriod'] ?? 9;
            }
            debugPrint('📊 KST enabled with signal period: $_kstSignalPeriod');
            break;
        }
      }
    } catch (e) {
      debugPrint('❌ Error prefilling indicators: $e');
    }
  }

  Future<void> _loadUserWallets() async {
    try {
      await _walletService.loadUserWallets();
      setState(() {
        _userWallets = _walletService.wallets;
        _selectedWallet = _userWallets.isNotEmpty ? _userWallets.first : null;
        _isLoadingWallets = false;
      });
      debugPrint('📱 Loaded ${_userWallets.length} wallets for bot creation');
    } catch (e) {
      debugPrint('❌ Error loading wallets: $e');
      setState(() {
        _isLoadingWallets = false;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _athController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white24,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          _buildHeader(),

          // Content
          Expanded(
            child: SingleChildScrollView(
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: _buildCurrentSectionContent(),
            ),
          ),

          // Fixed bottom button
          _buildBottomButton(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          if (_currentSection != BotConfigSection.main)
            IconButton(
              onPressed: () {
                final wasIndicatorSettings =
                    _currentSection == BotConfigSection.indicatorSettings;
                setState(() => _currentSection = BotConfigSection.main);
                if (wasIndicatorSettings) {
                  _restoreScrollPosition();
                }
              },
              icon: const Icon(
                LucideIcons.arrowLeft,
                color: Colors.white,
                size: 20,
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),

          if (_currentSection != BotConfigSection.main)
            const SizedBox(width: 12),

          Expanded(
            child: Text(
              _getSectionTitle(),
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),

          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(LucideIcons.x, color: Colors.white, size: 20),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  String _getSectionTitle() {
    switch (_currentSection) {
      case BotConfigSection.main:
        return widget.isEditMode ? 'EDIT BOT' : 'CONFIGURE BOT';
      case BotConfigSection.strategySelection:
        return 'Select Strategy';
      case BotConfigSection.solPerTradeSettings:
        return 'SOL per Trade';
      case BotConfigSection.maxTradesSettings:
        return 'Max Daily Trades';
      case BotConfigSection.riskSettings:
        return 'Risk Settings';
      case BotConfigSection.timezoneSettings:
        return 'Timezone Settings';
      case BotConfigSection.tradingHoursSettings:
        return 'Trading Hours';
      case BotConfigSection.indicatorSettings:
        return '$_selectedIndicator Settings';
      case BotConfigSection.addNewIndicator:
        return 'Add New Indicator';
    }
  }

  Widget _buildCurrentSectionContent() {
    switch (_currentSection) {
      case BotConfigSection.main:
        return _buildMainContent();
      case BotConfigSection.strategySelection:
        return _buildStrategySelection();
      case BotConfigSection.solPerTradeSettings:
        return _buildSolPerTradeSettings();
      case BotConfigSection.maxTradesSettings:
        return _buildMaxTradesSettings();
      case BotConfigSection.riskSettings:
        return _buildRiskSettings();
      case BotConfigSection.timezoneSettings:
        return _buildTimezoneSettings();
      case BotConfigSection.tradingHoursSettings:
        return _buildTradingHoursSettings();
      case BotConfigSection.indicatorSettings:
        return _buildIndicatorSettings();
      case BotConfigSection.addNewIndicator:
        return _buildAddNewIndicatorList();
    }
  }

  Widget _buildBottomButton() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(top: BorderSide(color: Colors.white24, width: 1)),
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          child: Container(
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(12),
            ),
            child: ElevatedButton(
              onPressed: _isCreating
                  ? null
                  : () async {
                      if (_currentSection == BotConfigSection.main &&
                          widget.isCreating) {
                        // Only create bot when on main section and in creating mode
                        await _createTradingBot();
                      } else if (_currentSection == BotConfigSection.main &&
                          widget.isEditMode) {
                        // Update existing bot configuration
                        await _updateTradingBot();
                      } else if (_currentSection == BotConfigSection.main &&
                          !widget.isCreating) {
                        // Save configuration for existing bot (legacy)
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Bot configuration saved!'),
                            backgroundColor: Colors.green,
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      } else {
                        // Save settings for current section and go back to main
                        final sectionName = _getSectionName(_currentSection);
                        _navigateBackToMainWithScrollPosition();
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('$sectionName settings saved!'),
                            backgroundColor: Colors.green,
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      }
                    },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                shadowColor: Colors.transparent,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                _currentSection == BotConfigSection.main
                    ? (widget.isCreating
                          ? (_isCreating ? 'Creating Bot...' : 'Create Bot')
                          : widget.isEditMode
                          ? (_isCreating ? 'Updating Bot...' : 'Update Bot')
                          : 'Save Configuration')
                    : _currentSection == BotConfigSection.addNewIndicator
                    ? 'Create Indicator'
                    : 'Save Settings',
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Wallet Selection Section
        _buildWalletSection(),

        const SizedBox(height: 24),

        // Trading Strategy Section
        _buildConfigSection('Trading Strategy', [
          _buildClickableConfigItem(
            'Strategy Type',
            _selectedStrategy,
            LucideIcons.trendingUp,
            () => setState(
              () => _currentSection = BotConfigSection.strategySelection,
            ),
          ),
          _buildClickableConfigItem(
            'SOL per Trade',
            '${_solPerTrade.toStringAsFixed(3)} SOL',
            LucideIcons.coins,
            () => setState(
              () => _currentSection = BotConfigSection.solPerTradeSettings,
            ),
          ),
          _buildClickableConfigItem(
            'Max Daily Trades',
            '$_maxDailyTrades',
            LucideIcons.clock,
            () => setState(
              () => _currentSection = BotConfigSection.maxTradesSettings,
            ),
          ),
        ]),

        const SizedBox(height: 24),

        // Risk Management Section
        _buildConfigSection('Risk Management', [
          _buildClickableConfigItem(
            'Stop Loss',
            '${_stopLossPercentage.toStringAsFixed(1)}%',
            LucideIcons.shield,
            () =>
                setState(() => _currentSection = BotConfigSection.riskSettings),
          ),
          _buildClickableConfigItem(
            'Take Profit',
            '${_takeProfitPercentage.toStringAsFixed(1)}%',
            LucideIcons.target,
            () =>
                setState(() => _currentSection = BotConfigSection.riskSettings),
          ),
          _buildConfigItem('Max Drawdown', '20%', LucideIcons.trendingDown),
        ]),

        const SizedBox(height: 24),

        // All Time High Section
        _buildConfigSection('All Time High', [_buildATHInputField()]),

        const SizedBox(height: 24),

        // Market Cap Range Section
        _buildConfigSection('Market Cap Range', [
          _buildMinMarketCapInputField(),
          const SizedBox(height: 16),
          _buildMaxMarketCapInputField(),
        ]),

        const SizedBox(height: 24),

        // Risk Level Section
        _buildConfigSection('Risk Level', [_buildRiskLevelSelection()]),

        const SizedBox(height: 24),

        // Trading Hours Section
        _buildConfigSection('Trading Hours', [
          _buildClickableConfigItem(
            '24/7 Trading',
            _trading24_7 ? 'Enabled' : 'Custom Hours',
            LucideIcons.clock,
            () => setState(
              () => _currentSection = BotConfigSection.tradingHoursSettings,
            ),
          ),
          _buildClickableConfigItem(
            'Timezone',
            _selectedTimezone,
            LucideIcons.globe,
            () => setState(
              () => _currentSection = BotConfigSection.timezoneSettings,
            ),
          ),
          _buildClickableConfigItem(
            'Market Hours Only',
            _marketHoursOnly ? 'Enabled' : 'Disabled',
            LucideIcons.calendar,
            () => setState(() => _marketHoursOnly = !_marketHoursOnly),
          ),
        ]),

        const SizedBox(height: 24),

        // Technical Indicators Section
        _buildConfigSection('Technical Indicators', [
          _buildIndicatorToggle(
            'RSI ($_rsiPeriod)',
            _useRSI,
            'Relative Strength Index',
            () {
              _saveScrollPosition();
              setState(() {
                _selectedIndicator = 'RSI ($_rsiPeriod)';
                _currentSection = BotConfigSection.indicatorSettings;
              });
              _scrollToTop();
            },
          ),
          _buildIndicatorToggle(
            'MACD',
            _useMACD,
            'Moving Average Convergence Divergence',
            () {
              _saveScrollPosition();
              setState(() {
                _selectedIndicator = 'MACD';
                _currentSection = BotConfigSection.indicatorSettings;
              });
              _scrollToTop();
            },
          ),
          _buildIndicatorToggle(
            'Bollinger Bands',
            _useBollingerBands,
            'Price volatility indicator',
            () {
              _saveScrollPosition();
              setState(() {
                _selectedIndicator = 'Bollinger Bands';
                _currentSection = BotConfigSection.indicatorSettings;
              });
              _scrollToTop();
            },
          ),
          _buildIndicatorToggle(
            'EMA ($_emaFastPeriod/$_emaSlowPeriod)',
            _useEMA,
            'Exponential Moving Average',
            () {
              _saveScrollPosition();
              setState(() {
                _selectedIndicator = 'EMA ($_emaFastPeriod/$_emaSlowPeriod)';
                _currentSection = BotConfigSection.indicatorSettings;
              });
              _scrollToTop();
            },
          ),
          _buildIndicatorToggle(
            'Volume Profile',
            _useVolumeProfile,
            'Trading volume analysis',
            () {
              _saveScrollPosition();
              setState(() {
                _selectedIndicator = 'Volume Profile';
                _currentSection = BotConfigSection.indicatorSettings;
              });
              _scrollToTop();
            },
          ),
          _buildIndicatorToggle(
            'KST',
            _useKST,
            'Know Sure Thing Oscillator',
            () {
              _saveScrollPosition();
              setState(() {
                _selectedIndicator = 'KST';
                _currentSection = BotConfigSection.indicatorSettings;
              });
              _scrollToTop();
            },
          ),

          // Add new indicator option
          _buildAddNewIndicatorOption(),
        ]),

        const SizedBox(height: 100), // Space for fixed button
      ],
    );
  }

  Widget _buildWalletSection() {
    return _buildConfigSection('Wallet Selection', [
      if (_isLoadingWallets)
        Container(
          padding: const EdgeInsets.all(16),
          child: const Row(
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              SizedBox(width: 12),
              Text(
                'Loading wallets...',
                style: TextStyle(color: Colors.white70),
              ),
            ],
          ),
        )
      else if (_userWallets.isEmpty)
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              Icon(LucideIcons.info, color: Colors.orange, size: 20),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  'No wallets found. Please create a wallet in the Wallet tab first.',
                  style: TextStyle(color: Colors.orange),
                ),
              ),
            ],
          ),
        )
      else
        ..._userWallets.map((wallet) => _buildWalletOption(wallet)).toList(),
    ]);
  }

  Widget _buildWalletOption(Wallet wallet) {
    final isSelected = _selectedWallet?.id == wallet.id;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: isSelected
            ? LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xFF1E3A8A).withValues(alpha: 0.9),
                  const Color(0xFF3B82F6).withValues(alpha: 0.7),
                  const Color(0xFF60A5FA).withValues(alpha: 0.5),
                ],
              )
            : null,
        color: isSelected ? null : Colors.white.withValues(alpha: 0.05),
        border: isSelected
            ? null
            : Border.all(color: Colors.white.withValues(alpha: 0.1), width: 1),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: Colors.blue.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            setState(() {
              _selectedWallet = wallet;
            });
            debugPrint(
              '📱 Selected wallet: ${wallet.name} (${wallet.balance} SOL)',
            );
          },
          borderRadius: BorderRadius.circular(isSelected ? 10 : 12),
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Wallet icon
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? Colors.blue
                        : Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    LucideIcons.wallet,
                    color: isSelected ? Colors.white : Colors.white70,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),

                // Wallet details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        wallet.name,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: isSelected
                              ? FontWeight.w600
                              : FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${wallet.balance.toStringAsFixed(4)} SOL',
                        style: TextStyle(color: Colors.white70, fontSize: 14),
                      ),
                      if (wallet.type != 'manual')
                        Text(
                          wallet.type.toUpperCase(),
                          style: TextStyle(
                            color: Colors.blue,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                    ],
                  ),
                ),

                // Selection indicator
                if (isSelected)
                  const Icon(LucideIcons.check, color: Colors.blue, size: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStrategySelection() {
    final strategies = [
      {
        'name': 'DCA',
        'description': 'Dollar Cost Averaging - Regular interval investments',
      },
      {
        'name': 'Grid Trading',
        'description': 'Place buy and sell orders at regular intervals',
      },
      {'name': 'Momentum', 'description': 'Follow price trends and momentum'},
      {
        'name': 'Mean Reversion',
        'description': 'Buy low, sell high based on averages',
      },
      {'name': 'Scalping', 'description': 'High-frequency short-term trades'},
    ];

    return Column(
      children: strategies.map((strategy) {
        final isSelected = _selectedStrategy == strategy['name'];
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                setState(() {
                  _selectedStrategy = strategy['name']!;
                  _currentSection = BotConfigSection.main;
                });
              },
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isSelected
                      ? Colors.white.withValues(alpha: 0.2)
                      : Colors.white.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.1),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isSelected
                              ? Colors.white
                              : Colors.white.withValues(alpha: 0.4),
                          width: 2,
                        ),
                      ),
                      child: isSelected
                          ? Center(
                              child: Container(
                                width: 10,
                                height: 10,
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.white,
                                ),
                              ),
                            )
                          : null,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            strategy['name']!,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            strategy['description']!,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.white.withValues(alpha: 0.6),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSolPerTradeSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'SOL per Trade Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting(
          'SOL Amount',
          _solPerTrade,
          0.01,
          10.0,
          (value) => setState(() => _solPerTrade = value),
        ),

        const SizedBox(height: 20),

        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Estimated Trade Value',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '\$${(_solPerTrade * 150).toStringAsFixed(2)} USD',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Based on current SOL price (~\$150)',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMaxTradesSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Max Daily Trades Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting(
          'Max Trades per Day',
          _maxDailyTrades.toDouble(),
          1,
          200,
          (value) => setState(() => _maxDailyTrades = value.toInt()),
        ),

        const SizedBox(height: 20),

        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Trading Frequency',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '${(1440 / _maxDailyTrades).toStringAsFixed(1)} minutes',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Average time between trades',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 20),

        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              Icon(LucideIcons.info, color: Colors.white, size: 20),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Trading Tip',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Higher trade limits allow for more opportunities but may increase risk. Consider market volatility when setting limits.',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildIndicatorSettings() {
    // Handle dynamic indicator names
    if (_selectedIndicator.startsWith('RSI')) {
      return _buildRSISettings();
    } else if (_selectedIndicator.startsWith('EMA')) {
      return _buildEMASettings();
    }

    switch (_selectedIndicator) {
      case 'MACD':
        return _buildMACDSettings();
      case 'Bollinger Bands':
        return _buildBollingerSettings();
      case 'Volume Profile':
        return _buildVolumeSettings();
      case 'Stochastic RSI':
        return _buildStochasticRSISettings();
      case 'Williams %R':
        return _buildWilliamsRSettings();
      case 'Commodity Channel Index (CCI)':
        return _buildCCISettings();
      case 'Average True Range (ATR)':
        return _buildATRSettings();
      case 'Parabolic SAR':
        return _buildParabolicSARSettings();
      case 'Ichimoku Cloud':
        return _buildIchimokuSettings();
      case 'KST':
        return _buildKSTSettings();
      default:
        return _buildDefaultIndicatorSettings();
    }
  }

  Widget _buildRSISettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'RSI Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        // Period setting with drag and type
        _buildDragAndTypeField(
          'Period',
          _rsiPeriod.toDouble(),
          5,
          50,
          (value) => setState(() => _rsiPeriod = value.toInt()),
        ),

        const SizedBox(height: 24),

        // Buy Configuration Header
        const Text(
          'Buy Configuration',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.green,
          ),
        ),
        const SizedBox(height: 12),

        _buildDragAndTypeField(
          'Oversold Level (0-50)',
          _rsiOversold.toDouble(),
          0,
          50,
          (value) => setState(() => _rsiOversold = value.toInt()),
        ),

        const SizedBox(height: 24),

        // Sell Configuration Header
        const Text(
          'Sell Configuration',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.red,
          ),
        ),
        const SizedBox(height: 12),

        _buildDragAndTypeField(
          'Overbought Level (50-100)',
          _rsiOverbought.toDouble(),
          50,
          100,
          (value) => setState(() => _rsiOverbought = value.toInt()),
        ),
      ],
    );
  }

  Widget _buildMACDSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'MACD Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('Fast Period', 12, 5, 20, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Slow Period', 26, 20, 50, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Signal Period', 9, 5, 15, (value) {}),
      ],
    );
  }

  Widget _buildBollingerSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Bollinger Bands Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('Period', 20, 10, 50, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Standard Deviation', 2, 1, 3, (value) {}),
      ],
    );
  }

  Widget _buildEMASettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'EMA Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        // Fast Period setting
        _buildDragAndTypeField(
          'Fast Period (5-20)',
          _emaFastPeriod.toDouble(),
          5,
          20,
          (value) => setState(() => _emaFastPeriod = value.toInt()),
        ),

        const SizedBox(height: 20),

        // Slow Period setting
        _buildDragAndTypeField(
          'Slow Period (21-100)',
          _emaSlowPeriod.toDouble(),
          21,
          100,
          (value) => setState(() => _emaSlowPeriod = value.toInt()),
        ),

        const SizedBox(height: 24),

        // Buy Configuration Header
        const Text(
          'Buy Configuration',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.green,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Bullish crossover: Fast EMA crosses above Slow EMA',
          style: TextStyle(fontSize: 14, color: Colors.white70),
        ),

        const SizedBox(height: 16),

        // Sell Configuration Header
        const Text(
          'Sell Configuration',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.red,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Bearish crossover: Fast EMA crosses below Slow EMA',
          style: TextStyle(fontSize: 14, color: Colors.white70),
        ),
      ],
    );
  }

  Widget _buildKSTSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'KST Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        // ROC Periods
        const Text(
          'ROC Periods',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.blue,
          ),
        ),
        const SizedBox(height: 12),
        _buildKSTPeriodsField(
          'ROC Periods [10, 15, 20, 30]',
          _kstRocPeriods.join(', '),
          Colors.blue,
          (value) {
            final periods = value
                .split(',')
                .map((e) => int.tryParse(e.trim()))
                .where((e) => e != null)
                .cast<int>()
                .toList();
            if (periods.length == 4) {
              setState(() => _kstRocPeriods = periods);
            } else {
              debugPrint(
                '⚠️ ROC Periods must have exactly 4 values, got ${periods.length}',
              );
            }
          },
        ),

        const SizedBox(height: 20),

        // SMA Periods
        const Text(
          'SMA Periods',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.orange,
          ),
        ),
        const SizedBox(height: 12),
        _buildKSTPeriodsField(
          'SMA Periods [10, 10, 10, 15]',
          _kstSmaPeriods.join(', '),
          Colors.orange,
          (value) {
            final periods = value
                .split(',')
                .map((e) => int.tryParse(e.trim()))
                .where((e) => e != null)
                .cast<int>()
                .toList();
            if (periods.length == 4) {
              setState(() => _kstSmaPeriods = periods);
            } else {
              debugPrint(
                '⚠️ SMA Periods must have exactly 4 values, got ${periods.length}',
              );
            }
          },
        ),

        const SizedBox(height: 20),

        // Signal Period
        _buildDragAndTypeField(
          'Signal Period',
          _kstSignalPeriod.toDouble(),
          5,
          20,
          (value) => setState(() => _kstSignalPeriod = value.toInt()),
        ),

        const SizedBox(height: 24),

        // Buy Configuration Header
        const Text(
          'Buy Configuration',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.green,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Momentum up: KST line crosses above signal line',
          style: TextStyle(fontSize: 14, color: Colors.white70),
        ),

        const SizedBox(height: 16),

        // Sell Configuration Header
        const Text(
          'Sell Configuration',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.red,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Momentum down: KST line crosses below signal line',
          style: TextStyle(fontSize: 14, color: Colors.white70),
        ),
      ],
    );
  }

  Widget _buildVolumeSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Volume Profile Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('Lookback Period', 100, 50, 500, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Volume Threshold', 150, 100, 300, (value) {}),
      ],
    );
  }

  Widget _buildStochasticRSISettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Stochastic RSI Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('K Period', 14, 5, 50, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('D Period', 3, 1, 10, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('RSI Period', 14, 5, 50, (value) {}),
      ],
    );
  }

  Widget _buildWilliamsRSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Williams %R Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('Period', 14, 5, 50, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Overbought Level', -20, -30, -10, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Oversold Level', -80, -90, -70, (value) {}),
      ],
    );
  }

  Widget _buildCCISettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'CCI Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('Period', 20, 5, 50, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Overbought Level', 100, 50, 200, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Oversold Level', -100, -200, -50, (value) {}),
      ],
    );
  }

  Widget _buildATRSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'ATR Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('Period', 14, 5, 50, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Multiplier', 2, 1, 5, (value) {}),
      ],
    );
  }

  Widget _buildParabolicSARSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Parabolic SAR Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('Start AF', 0.02, 0.01, 0.1, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Max AF', 0.2, 0.1, 0.5, (value) {}),
      ],
    );
  }

  Widget _buildIchimokuSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Ichimoku Cloud Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('Tenkan Period', 9, 5, 20, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Kijun Period', 26, 15, 50, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Senkou B Period', 52, 30, 100, (value) {}),
      ],
    );
  }

  Widget _buildDefaultIndicatorSettings() {
    return const Column(
      children: [
        Text(
          'No specific settings available for this indicator.',
          style: TextStyle(fontSize: 16, color: Colors.white),
        ),
      ],
    );
  }

  Widget _buildRiskSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Risk Management',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting(
          'Stop Loss (%)',
          _stopLossPercentage,
          1,
          20,
          (value) => setState(() => _stopLossPercentage = value),
        ),

        const SizedBox(height: 20),

        _buildSliderSetting(
          'Take Profit (%)',
          _takeProfitPercentage,
          5,
          50,
          (value) => setState(() => _takeProfitPercentage = value),
        ),
      ],
    );
  }

  Widget _buildTimezoneSettings() {
    final timezones = ['UTC', 'EST', 'PST', 'GMT', 'CET', 'JST', 'AEST'];

    return Column(
      children: timezones.map((timezone) {
        final isSelected = _selectedTimezone == timezone;
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                setState(() {
                  _selectedTimezone = timezone;
                  _currentSection = BotConfigSection.main;
                });
              },
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isSelected
                      ? Colors.white.withValues(alpha: 0.2)
                      : Colors.white.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.1),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isSelected
                              ? Colors.white
                              : Colors.white.withValues(alpha: 0.4),
                          width: 2,
                        ),
                      ),
                      child: isSelected
                          ? Center(
                              child: Container(
                                width: 10,
                                height: 10,
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.white,
                                ),
                              ),
                            )
                          : null,
                    ),
                    const SizedBox(width: 16),
                    Text(
                      timezone,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildTradingHoursSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 24/7 Trading Toggle
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '24/7 Trading',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Trade continuously without breaks',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
              Switch(
                value: _trading24_7,
                onChanged: (value) => setState(() => _trading24_7 = value),
                activeColor: AppTheme.primaryColor,
                activeTrackColor: AppTheme.primaryColor.withValues(alpha: 0.3),
                inactiveThumbColor: Colors.white.withValues(alpha: 0.6),
                inactiveTrackColor: Colors.white.withValues(alpha: 0.1),
              ),
            ],
          ),
        ),

        if (!_trading24_7) ...[
          const SizedBox(height: 24),
          const Text(
            'Custom Trading Hours',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),

          // Start Time
          _buildTimeSelector('Start Time', _tradingStartTime, (time) {
            setState(() => _tradingStartTime = time);
          }),

          const SizedBox(height: 12),

          // End Time
          _buildTimeSelector('End Time', _tradingEndTime, (time) {
            setState(() => _tradingEndTime = time);
          }),
        ],

        const SizedBox(height: 32),
      ],
    );
  }

  Widget _buildTimeSelector(
    String label,
    String time,
    Function(String) onChanged,
  ) {
    return GestureDetector(
      onTap: () => _showTimePicker(label, time, onChanged),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
            Row(
              children: [
                Text(
                  time,
                  style: TextStyle(
                    fontSize: 16,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  LucideIcons.clock,
                  color: Colors.white.withValues(alpha: 0.4),
                  size: 16,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showTimePicker(
    String label,
    String currentTime,
    Function(String) onChanged,
  ) async {
    // Parse current time
    final timeParts = currentTime.split(':');
    final hour = int.tryParse(timeParts[0]) ?? 9;
    final minute = int.tryParse(timeParts[1]) ?? 0;

    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay(hour: hour, minute: minute),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.dark(
              primary: AppTheme.primaryColor,
              onPrimary: Colors.white,
              surface: const Color(0xFF1E1E1E),
              onSurface: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      final formattedTime =
          '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      onChanged(formattedTime);
    }
  }

  Widget _buildConfigSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildConfigItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: Colors.white, size: 16),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClickableConfigItem(
    String label,
    String value,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: Colors.white, size: 16),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    label,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  LucideIcons.chevronRight,
                  color: Colors.white.withValues(alpha: 0.4),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIndicatorToggle(
    String label,
    bool value,
    String description,
    VoidCallback onSettingsTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          Switch(
            value: value,
            onChanged: (newValue) {
              setState(() {
                // Handle dynamic labels
                if (label.startsWith('RSI')) {
                  _useRSI = newValue;
                } else if (label.startsWith('EMA')) {
                  _useEMA = newValue;
                } else {
                  switch (label) {
                    case 'MACD':
                      _useMACD = newValue;
                      break;
                    case 'Bollinger Bands':
                      _useBollingerBands = newValue;
                      break;
                    case 'Volume Profile':
                      _useVolumeProfile = newValue;
                      break;
                    case 'KST':
                      _useKST = newValue;
                      break;
                  }
                }
              });
            },
            activeColor: Colors.white,
            activeTrackColor: Colors.white.withValues(alpha: 0.3),
            inactiveThumbColor: Colors.white.withValues(alpha: 0.6),
            inactiveTrackColor: Colors.white.withValues(alpha: 0.1),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
          if (value)
            GestureDetector(
              onTap: onSettingsTap,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  LucideIcons.settings,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAddNewIndicatorOption() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // Show add new indicator dialog
            _showAddIndicatorDialog();
          },
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                style: BorderStyle.solid,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    LucideIcons.plus,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Add New Indicator',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
                Icon(
                  LucideIcons.chevronRight,
                  color: Colors.white.withValues(alpha: 0.6),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSliderSetting(
    String label,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              Text(
                value < 1 ? value.toStringAsFixed(3) : value.toInt().toString(),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Colors.white,
              inactiveTrackColor: Colors.white.withValues(alpha: 0.2),
              thumbColor: Colors.white,
              overlayColor: Colors.white.withValues(alpha: 0.2),
              trackHeight: 4,
            ),
            child: Slider(
              value: value,
              min: min,
              max: max,
              divisions: (max - min).toInt(),
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDragAndTypeField(
    String label,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
  ) {
    final TextEditingController controller = TextEditingController(
      text: value < 1 ? value.toStringAsFixed(3) : value.toInt().toString(),
    );

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),

          // Slider for dragging
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Colors.white,
              inactiveTrackColor: Colors.white.withValues(alpha: 0.2),
              thumbColor: Colors.white,
              overlayColor: Colors.white.withValues(alpha: 0.2),
              trackHeight: 4,
            ),
            child: Slider(
              value: value,
              min: min,
              max: max,
              divisions: (max - min).toInt(),
              onChanged: (newValue) {
                onChanged(newValue);
                controller.text = newValue < 1
                    ? newValue.toStringAsFixed(3)
                    : newValue.toInt().toString();
              },
            ),
          ),

          const SizedBox(height: 12),

          // Text field for typing
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: controller,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  style: const TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: 'Enter value',
                    hintStyle: TextStyle(
                      color: Colors.white.withValues(alpha: 0.5),
                    ),
                    filled: true,
                    fillColor: Colors.white.withValues(alpha: 0.05),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Colors.white.withValues(alpha: 0.2),
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Colors.white.withValues(alpha: 0.2),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Colors.blue),
                    ),
                  ),
                  onChanged: (text) {
                    final newValue = double.tryParse(text);
                    if (newValue != null &&
                        newValue >= min &&
                        newValue <= max) {
                      onChanged(newValue);
                    }
                  },
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 16,
                ),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.blue.withValues(alpha: 0.5),
                    width: 1,
                  ),
                ),
                child: Text(
                  '${min.toInt()}-${max.toInt()}',
                  style: const TextStyle(
                    color: Colors.blue,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildKSTPeriodsField(
    String label,
    String value,
    Color color,
    ValueChanged<String> onChanged,
  ) {
    final TextEditingController controller = TextEditingController(text: value);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),

          // Text field for typing periods
          TextField(
            controller: controller,
            keyboardType: TextInputType.text,
            style: const TextStyle(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'Enter 4 comma-separated values (e.g., 10, 15, 20, 30)',
              hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.5)),
              filled: true,
              fillColor: Colors.white.withValues(alpha: 0.05),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: Colors.white.withValues(alpha: 0.2),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: Colors.white.withValues(alpha: 0.2),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: color),
              ),
            ),
            onChanged: onChanged,
          ),

          const SizedBox(height: 8),
          Text(
            'Enter exactly 4 comma-separated numbers for the periods',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  void _saveScrollPosition() {
    if (_scrollController.hasClients) {
      _savedScrollPosition = _scrollController.offset;
      debugPrint('📍 Saved scroll position: $_savedScrollPosition');
    }
  }

  void _restoreScrollPosition() {
    if (_scrollController.hasClients) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.jumpTo(_savedScrollPosition);
          debugPrint('📍 Restored scroll position: $_savedScrollPosition');
        }
      });
    }
  }

  void _scrollToTop() {
    if (_scrollController.hasClients) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.jumpTo(0);
          debugPrint('📍 Scrolled to top');
        }
      });
    }
  }

  void _showAddIndicatorDialog() {
    setState(() => _currentSection = BotConfigSection.addNewIndicator);
  }

  Widget _buildAddNewIndicatorList() {
    final availableIndicators = [
      {
        'name': 'Stochastic RSI',
        'description':
            'Combines Stochastic oscillator with RSI for momentum analysis',
        'enabled': false,
      },
      {
        'name': 'Williams %R',
        'description':
            'Momentum indicator measuring overbought/oversold levels',
        'enabled': false,
      },
      {
        'name': 'Commodity Channel Index (CCI)',
        'description': 'Identifies cyclical trends in commodity prices',
        'enabled': false,
      },
      {
        'name': 'Average True Range (ATR)',
        'description': 'Measures market volatility',
        'enabled': false,
      },
      {
        'name': 'Parabolic SAR',
        'description': 'Determines potential reversal points in price',
        'enabled': false,
      },
      {
        'name': 'Ichimoku Cloud',
        'description':
            'Comprehensive indicator showing support, resistance, and momentum',
        'enabled': false,
      },
    ];

    return Column(
      children: [
        ...availableIndicators.map((indicator) {
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  _saveScrollPosition();
                  setState(() {
                    _selectedIndicator = indicator['name'] as String;
                    _currentSection = BotConfigSection.indicatorSettings;
                  });
                  _scrollToTop();
                },
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.1),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          LucideIcons.trendingUp,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              indicator['name'] as String,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              indicator['description'] as String,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.white.withValues(alpha: 0.6),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Icon(
                        LucideIcons.chevronRight,
                        color: Colors.white.withValues(alpha: 0.4),
                        size: 16,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }).toList(),

        const SizedBox(height: 100), // Space for fixed button
      ],
    );
  }

  String _getSectionName(BotConfigSection section) {
    switch (section) {
      case BotConfigSection.main:
        return 'Main';
      case BotConfigSection.strategySelection:
        return 'Strategy';
      case BotConfigSection.solPerTradeSettings:
        return 'Sol Per Trade';
      case BotConfigSection.maxTradesSettings:
        return 'Max Trades';
      case BotConfigSection.riskSettings:
        return 'Risk';
      case BotConfigSection.timezoneSettings:
        return 'Timezone';
      case BotConfigSection.tradingHoursSettings:
        return 'Trading Hours';
      case BotConfigSection.indicatorSettings:
        return 'Indicator';
      case BotConfigSection.addNewIndicator:
        return 'New Indicator';
    }
  }

  void _navigateBackToMainWithScrollPosition() {
    // Store current scroll position
    final currentScrollOffset = _scrollController.hasClients
        ? _scrollController.offset
        : 0.0;

    // Navigate back to main
    setState(() => _currentSection = BotConfigSection.main);

    // Restore scroll position after the widget rebuilds
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients && mounted) {
        _scrollController.animateTo(
          currentScrollOffset,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  Widget _buildATHInputField() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(LucideIcons.trendingUp, color: Colors.orange, size: 20),
              const SizedBox(width: 8),
              Text(
                'All Time High Value',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _athController,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  style: const TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: 'Enter value (e.g., 150)',
                    hintStyle: TextStyle(
                      color: Colors.white.withValues(alpha: 0.5),
                    ),
                    filled: true,
                    fillColor: Colors.white.withValues(alpha: 0.05),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Colors.white.withValues(alpha: 0.2),
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Colors.white.withValues(alpha: 0.2),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Colors.blue),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      // Convert K value to actual value (e.g., 150 -> 150000)
                      final inputValue = double.tryParse(value);
                      _allTimeHighValue = inputValue != null
                          ? inputValue * 1000
                          : null;
                    });
                  },
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 16,
                ),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.5),
                    width: 1,
                  ),
                ),
                child: const Text(
                  'K',
                  style: TextStyle(
                    color: Colors.orange,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Enter value in thousands (e.g., 150 = 150,000). Used to track price performance.',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRiskLevelSelection() {
    final riskLevels = [
      {
        'name': 'Good',
        'icon': LucideIcons.shield,
        'color': Colors.green,
        'description': 'Low risk, conservative trading',
      },
      {
        'name': 'Warning',
        'icon': LucideIcons.triangle,
        'color': Colors.orange,
        'description': 'Medium risk, balanced approach',
      },
      {
        'name': 'Danger',
        'icon': LucideIcons.circle,
        'color': Colors.red,
        'description': 'High risk, aggressive trading',
      },
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Select Risk Levels',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Multi-select',
                  style: TextStyle(
                    color: Colors.blue,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...riskLevels.map((risk) {
            final isSelected = _selectedRiskLevels.contains(risk['name']);
            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    setState(() {
                      if (isSelected) {
                        // Don't allow deselecting if it's the only one selected
                        if (_selectedRiskLevels.length > 1) {
                          _selectedRiskLevels.remove(risk['name'] as String);
                        }
                      } else {
                        _selectedRiskLevels.add(risk['name'] as String);
                      }
                    });
                  },
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? (risk['color'] as Color).withValues(alpha: 0.1)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected
                            ? (risk['color'] as Color)
                            : Colors.white.withValues(alpha: 0.1),
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          risk['icon'] as IconData,
                          color: risk['color'] as Color,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                risk['name'] as String,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                risk['description'] as String,
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.6),
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          Icon(
                            LucideIcons.check,
                            color: risk['color'] as Color,
                            size: 16,
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Future<void> _createTradingBot() async {
    if (_isCreating) return; // Prevent double submission

    // Save scroll position before creating bot
    _saveScrollPosition();

    // Validate wallet selection
    if (_selectedWallet == null) {
      ToastUtils.showError(
        context,
        'Please select a wallet for the trading bot',
      );
      return;
    }

    // Validate wallet has sufficient balance
    if (_selectedWallet!.balance < _solPerTrade) {
      ToastUtils.showError(
        context,
        'Selected wallet has insufficient balance. Required: ${_solPerTrade.toStringAsFixed(4)} SOL, Available: ${_selectedWallet!.balance.toStringAsFixed(4)} SOL',
      );
      return;
    }

    // Validate that at least one indicator is enabled
    if (!_useRSI && !_useMACD && !_useBollingerBands && !_useEMA && !_useKST) {
      ToastUtils.showError(
        context,
        'Please enable at least one technical indicator before creating the bot',
      );
      return;
    }

    setState(() => _isCreating = true);

    try {
      debugPrint('🚀 Starting bot creation from BotConfigModal...');
      debugPrint('📊 Using actual form values:');
      debugPrint('  Strategy: $_selectedStrategy');
      debugPrint('  Sol per trade: $_solPerTrade');
      debugPrint('  Stop loss: $_stopLossPercentage%');
      debugPrint('  Take profit: $_takeProfitPercentage%');
      debugPrint('  Max daily trades: $_maxDailyTrades');
      debugPrint('  RSI enabled: $_useRSI (period: $_rsiPeriod)');
      debugPrint('  MACD enabled: $_useMACD');
      debugPrint('  Bollinger Bands enabled: $_useBollingerBands');
      debugPrint('  EMA enabled: $_useEMA');
      debugPrint(
        '📱 Selected wallet: ${_selectedWallet!.name} (ID: ${_selectedWallet!.id})',
      );
      debugPrint(
        '💰 Wallet balance: ${_selectedWallet!.balance.toStringAsFixed(4)} SOL',
      );
      debugPrint('  Trading 24/7: $_trading24_7');
      if (!_trading24_7) {
        debugPrint('  Trading hours: $_tradingStartTime - $_tradingEndTime');
      }

      // Use the actual coin data passed to the modal
      final coinData =
          widget.coinData ??
          CoinData(
            id: widget.bot.tokenSymbol.toLowerCase(),
            symbol: widget.bot.tokenSymbol.toUpperCase(),
            name: widget.bot.tokenSymbol.toUpperCase(),
            image: widget.bot.tokenImage.isNotEmpty
                ? widget.bot.tokenImage
                : null,
          );

      debugPrint('🪙 Creating bot with coin data:');
      debugPrint('🆔 Coin ID: ${coinData.id}');
      debugPrint('🏷️ Token Address: ${coinData.tokenAddress}');
      debugPrint('📊 Symbol: ${coinData.symbol}');
      debugPrint('📛 Name: ${coinData.name}');

      // Create indicators based on user selections
      final indicators = <TradingIndicator>[];

      if (_useRSI) {
        indicators.add(
          TradingIndicator(
            type: IndicatorType.rsi,
            parameters: {'period': _rsiPeriod},
            buyThreshold: _rsiOversold.toDouble(),
            sellThreshold: _rsiOverbought.toDouble(),
          ),
        );
      }

      if (_useMACD) {
        indicators.add(
          TradingIndicator(
            type: IndicatorType.macd,
            parameters: {'fastPeriod': 12, 'slowPeriod': 26, 'signalPeriod': 9},
            buyThreshold: 0.0,
            sellThreshold: 0.0,
          ),
        );
      }

      if (_useBollingerBands) {
        indicators.add(
          TradingIndicator(
            type: IndicatorType.bollinger,
            parameters: {'period': 20, 'standardDeviations': 2},
            buyThreshold: 0.0,
            sellThreshold: 0.0,
          ),
        );
      }

      if (_useEMA) {
        indicators.add(
          TradingIndicator(
            type: IndicatorType.ema,
            parameters: {
              'fastPeriod': _emaFastPeriod,
              'slowPeriod': _emaSlowPeriod,
            },
            buyThreshold: 0.0,
            sellThreshold: 0.0,
          ),
        );
      }

      if (_useKST) {
        indicators.add(
          TradingIndicator(
            type: IndicatorType.kst,
            parameters: {
              'rocPeriods': _kstRocPeriods,
              'smaPeriods': _kstSmaPeriods,
              'signalPeriod': _kstSignalPeriod,
            },
            buyThreshold: 0.0,
            sellThreshold: 0.0,
          ),
        );
      }

      // If no indicators selected, add default RSI
      if (indicators.isEmpty) {
        indicators.add(
          TradingIndicator(
            type: IndicatorType.rsi,
            parameters: {'period': 14},
            buyThreshold: 30.0,
            sellThreshold: 70.0,
          ),
        );
      }

      debugPrint(
        '📈 Created ${indicators.length} indicators based on user selection',
      );

      // Create trading bot in database using actual form values
      final result = await TradingBotService().createTradingBot(
        name: widget.bot.name,
        coin: coinData,
        strategy: _selectedStrategy,
        investmentAmount: _solPerTrade * 100, // Convert SOL to approximate USDT
        stopLoss: _stopLossPercentage,
        takeProfit: _takeProfitPercentage,
        indicators: indicators,
        startImmediately: true, // Start immediately as requested
        walletId: _selectedWallet!.id, // Pass selected wallet ID
        riskPercentage: 2.0, // Could add this as a form field later
        trading24_7: _trading24_7,
        tradingStartTime: _trading24_7 ? null : _tradingStartTime,
        tradingEndTime: _trading24_7 ? null : _tradingEndTime,
        maxDailyTrades: _maxDailyTrades,
        allTimeHighValue:
            _allTimeHighValue, // Stored as actual value (e.g., 150000)
        riskLevels: _selectedRiskLevels.toList(), // Multi-select risk levels
        minMarketCap:
            _minMarketCapValue, // Stored as actual value (e.g., 100000)
        maxMarketCap:
            _maxMarketCapValue, // Stored as actual value (e.g., 1000000)
      );

      if (result != null) {
        debugPrint('✅ Trading bot creation successful from BotConfigModal!');
        if (mounted) {
          Navigator.pop(context);
          widget.onBotCreated?.call();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Trading bot created successfully!'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } else {
        if (mounted) {
          ToastUtils.showError(context, 'Failed to create trading bot');
        }
      }
    } catch (e) {
      debugPrint('❌ Error creating trading bot: $e');
      if (mounted) {
        ToastUtils.showError(
          context,
          'Failed to create trading bot: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isCreating = false);
        // Restore scroll position after creating bot
        _restoreScrollPosition();
      }
    }
  }

  Future<void> _updateTradingBot() async {
    if (_isCreating) return; // Prevent double submission

    // Save scroll position before updating bot
    _saveScrollPosition();

    // Validate wallet selection
    if (_selectedWallet == null) {
      ToastUtils.showError(
        context,
        'Please select a wallet before updating the bot',
      );
      return;
    }

    setState(() {
      _isCreating = true;
    });

    try {
      debugPrint('🔄 Starting bot update process...');

      // Create CoinData from bot information
      final coinData = CoinData(
        id: widget.bot.tokenSymbol.toLowerCase(),
        symbol: widget.bot.tokenSymbol,
        name: widget.bot.name,
        image: widget.bot.tokenImage,
        currentPrice: widget.bot.currentPrice,
        marketCap: 0,
        marketCapRank: 0,
        fullyDilutedValuation: 0,
        totalVolume: 0,
        high24h: 0,
        low24h: 0,
        priceChange24h: 0,
        priceChangePercentage24h: widget.bot.priceChange24h,
        marketCapChange24h: 0,
        marketCapChangePercentage24h: 0,
        circulatingSupply: 0,
        totalSupply: 0,
        maxSupply: 0,
        ath: 0,
        athChangePercentage: 0,
        athDate: DateTime.now().toIso8601String(),
        atl: 0,
        atlChangePercentage: 0,
        atlDate: DateTime.now().toIso8601String(),
        lastUpdated: DateTime.now().toIso8601String(),
      );

      debugPrint('💰 Coin data prepared: ${coinData.symbol}');

      // Create indicators based on user selection
      final indicators = <TradingIndicator>[];

      // Add RSI if enabled
      if (_useRSI) {
        indicators.add(
          TradingIndicator(
            type: IndicatorType.rsi,
            parameters: {'period': _rsiPeriod},
            buyThreshold: _rsiOversold.toDouble(),
            sellThreshold: _rsiOverbought.toDouble(),
            isEnabled: true,
          ),
        );
      }

      // Add MACD if enabled
      if (_useMACD) {
        indicators.add(
          TradingIndicator(
            type: IndicatorType.macd,
            parameters: {'fastPeriod': 12, 'slowPeriod': 26, 'signalPeriod': 9},
            buyThreshold: 0.0, // MACD crossover
            sellThreshold: 0.0, // MACD crossover
            isEnabled: true,
          ),
        );
      }

      // Add Bollinger Bands if enabled
      if (_useBollingerBands) {
        indicators.add(
          TradingIndicator(
            type: IndicatorType.bollinger,
            parameters: {'period': 20, 'standardDeviations': 2.0},
            buyThreshold: 0.2, // Lower band touch
            sellThreshold: 0.8, // Upper band touch
            isEnabled: true,
          ),
        );
      }

      // Add EMA if enabled
      if (_useEMA) {
        indicators.add(
          TradingIndicator(
            type: IndicatorType.ema,
            parameters: {
              'fastPeriod': _emaFastPeriod,
              'slowPeriod': _emaSlowPeriod,
            },
            buyThreshold: 0.0,
            sellThreshold: 0.0,
            isEnabled: true,
          ),
        );
      }

      // Add KST if enabled
      if (_useKST) {
        indicators.add(
          TradingIndicator(
            type: IndicatorType.kst,
            parameters: {
              'rocPeriods': _kstRocPeriods,
              'smaPeriods': _kstSmaPeriods,
              'signalPeriod': _kstSignalPeriod,
            },
            buyThreshold: 0.0,
            sellThreshold: 0.0,
            isEnabled: true,
          ),
        );
      }

      debugPrint(
        '📈 Updated ${indicators.length} indicators based on user selection',
      );

      // Update trading bot in database using actual form values
      final result = await TradingBotService().updateTradingBot(
        botId: widget.bot.id,
        name: widget.bot.name,
        coin: coinData,
        strategy: _selectedStrategy,
        investmentAmount: _solPerTrade * 100, // Convert SOL to approximate USDT
        stopLoss: _stopLossPercentage,
        takeProfit: _takeProfitPercentage,
        indicators: indicators,
        startImmediately: widget.bot.isActive,
        walletId: _selectedWallet!.id,
        riskPercentage: 2.0,
        trading24_7: _trading24_7,
        tradingStartTime: _trading24_7 ? null : _tradingStartTime,
        tradingEndTime: _trading24_7 ? null : _tradingEndTime,
        maxDailyTrades: _maxDailyTrades,
        allTimeHighValue: _allTimeHighValue,
        riskLevels: _selectedRiskLevels.toList(),
        minMarketCap: _minMarketCapValue,
        maxMarketCap: _maxMarketCapValue,
      );

      if (result != null) {
        debugPrint('✅ Bot updated successfully: ${result['id']}');

        // Close modal
        Navigator.pop(context);

        // Call the callback to refresh the bot list
        widget.onBotUpdated?.call();

        // Show success message
        ToastUtils.showSuccess(
          context,
          'Bot "${widget.bot.name}" updated successfully!',
        );
      } else {
        throw Exception('Failed to update bot - no response from server');
      }
    } catch (e) {
      debugPrint('❌ Error updating trading bot: $e');
      ToastUtils.showError(context, 'Failed to update bot: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() {
          _isCreating = false;
        });
        // Restore scroll position after updating bot
        _restoreScrollPosition();
      }
    }
  }

  Widget _buildMinMarketCapInputField() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(LucideIcons.trendingDown, color: Colors.green, size: 20),
              const SizedBox(width: 8),
              Text(
                'Minimum Market Cap',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _minMarketCapController,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  style: const TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: 'Enter minimum value (e.g., 100)',
                    hintStyle: TextStyle(
                      color: Colors.white.withValues(alpha: 0.5),
                    ),
                    filled: true,
                    fillColor: Colors.white.withValues(alpha: 0.05),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Colors.white.withValues(alpha: 0.2),
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Colors.white.withValues(alpha: 0.2),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Colors.green),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      // Convert K value to actual value (e.g., 100 -> 100000)
                      final inputValue = double.tryParse(value);
                      _minMarketCapValue = inputValue != null
                          ? inputValue * 1000
                          : null;
                    });
                  },
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 16,
                ),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.green.withValues(alpha: 0.5),
                    width: 1,
                  ),
                ),
                child: const Text(
                  'K',
                  style: TextStyle(
                    color: Colors.green,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Enter minimum market cap in thousands (e.g., 100 = 100,000). Bot will only buy tokens above this market cap.',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMaxMarketCapInputField() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(LucideIcons.trendingUp, color: Colors.red, size: 20),
              const SizedBox(width: 8),
              Text(
                'Maximum Market Cap',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _maxMarketCapController,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  style: const TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: 'Enter maximum value (e.g., 1000)',
                    hintStyle: TextStyle(
                      color: Colors.white.withValues(alpha: 0.5),
                    ),
                    filled: true,
                    fillColor: Colors.white.withValues(alpha: 0.05),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Colors.white.withValues(alpha: 0.2),
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Colors.white.withValues(alpha: 0.2),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Colors.red),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      // Convert K value to actual value (e.g., 1000 -> 1000000)
                      final inputValue = double.tryParse(value);
                      _maxMarketCapValue = inputValue != null
                          ? inputValue * 1000
                          : null;
                    });
                  },
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 16,
                ),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.red.withValues(alpha: 0.5),
                    width: 1,
                  ),
                ),
                child: const Text(
                  'K',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Enter maximum market cap in thousands (e.g., 1000 = 1,000,000). Bot will only buy tokens below this market cap.',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}

class _CreateBotModalState extends State<CreateBotModal> {
  late old_model.TradingBot _dummyBot;

  @override
  void initState() {
    super.initState();
    // Create a dummy bot for configuration
    _dummyBot = old_model.TradingBot(
      id: 'temp',
      name: '${widget.coinData.symbol} Trader',
      tokenSymbol: widget.coinData.symbol,
      tokenImage: widget.coinData.image ?? '',
      currentPrice: widget.coinData.currentPrice ?? 0.0,
      priceChange24h: widget.coinData.priceChangePercentage24h ?? 0.0,
      isActive: false,
      strategy: 'DCA',
      solPerTrade: 0.1,
      totalTrades: 0,
      winRate: 0.0,
      totalEarned: 0.0,
      trades: [],
    );
  }

  @override
  Widget build(BuildContext context) {
    return BotConfigModal(
      bot: _dummyBot,
      isCreating: true,
      onBotCreated: widget.onBotCreated,
      coinData: widget.coinData,
    );
  }
}
