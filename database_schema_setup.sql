create table
  public.tokens (
    id uuid not null default gen_random_uuid (),
    coin_id text null,
    name text null,
    symbol text null,
    address text null,
    price double precision null,
    market_cap double precision null,
    liquidity double precision null,
    created_at timestamp with time zone null,
    data jsonb null,
    pair_created timestamp with time zone null,
    chain_id text null,
    dex_type text null,
    image_url text null,
    extra_data jsonb null,
    volume_h24 numeric null,
    price_change_h24 numeric null,
    updated_at timestamp with time zone null,
    constraint tokens_pkey primary key (id),
    constraint tokens_coin_id_key unique (coin_id)
  ) tablespace pg_default;



create table
  public.wallets (
    id uuid not null default gen_random_uuid (),
    user_id uuid not null,
    name text not null,
    type text not null,
    address text null,
    encrypted_private_key text null,
    balance numeric(20, 8) null default 0,
    is_active boolean null default false,
    created_at timestamp with time zone null default now(),
    updated_at timestamp with time zone null default now(),
    constraint wallets_pkey primary key (id),
    constraint wallets_user_id_fkey foreign key (user_id) references auth.users (id) on delete cascade,
    constraint wallets_type_check check (
      (
        type = any (
          array['paper'::text, 'solana'::text, 'external'::text]
        )
      )
    )
  ) tablespace pg_default;

create index if not exists idx_wallets_user_id on public.wallets using btree (user_id) tablespace pg_default;

create index if not exists idx_wallets_address on public.wallets using btree (address) tablespace pg_default;

create trigger handle_updated_at before
update on wallets for each row
execute function handle_updated_at ();



  create table
  public.trading_bots (
    id uuid not null default gen_random_uuid (),
    user_id uuid not null,
    name text not null,
    coin_id text not null,
    coin_symbol text not null,
    coin_name text not null,
    coin_image text null,
    strategy text not null,
    investment_amount numeric(20, 8) not null,
    stop_loss numeric(5, 2) null,
    take_profit numeric(5, 2) null,
    indicators jsonb null default '[]'::jsonb,
    is_active boolean null default false,
    is_archived boolean null default false,
    total_trades integer null default 0,
    win_rate numeric(5, 2) null default 0,
    total_earned numeric(20, 8) null default 0,
    created_at timestamp with time zone null default now(),
    updated_at timestamp with time zone null default now(),
    start_immediately boolean null default false,
    risk_percentage numeric(5, 2) null default 2.0,
    trading_24_7 boolean null default true,
    trading_start_time time without time zone null,
    trading_end_time time without time zone null,
    max_daily_trades integer null default 50,
    wallet_id uuid null,
    all_time_high_value numeric(20, 8) null,
    risk_levels text[] null,
    min_market_cap double precision null,
    max_market_cap double precision null,
    constraint trading_bots_pkey primary key (id),
    constraint trading_bots_user_id_fkey foreign key (user_id) references auth.users (id) on delete cascade,
    constraint trading_bots_wallet_id_fkey foreign key (wallet_id) references wallets (id),
    constraint check_risk_levels check (
      (
        risk_levels <@ array['Good'::text, 'Warning'::text, 'Danger'::text]
      )
    ),
    constraint check_ath_positive check (
      (
        (all_time_high_value is null)
        or (all_time_high_value > (0)::numeric)
      )
    )
  ) tablespace pg_default;

create index if not exists idx_trading_bots_user_id on public.trading_bots using btree (user_id) tablespace pg_default;

create index if not exists idx_trading_bots_active on public.trading_bots using btree (user_id, is_active) tablespace pg_default;

create index if not exists idx_trading_bots_is_active on public.trading_bots using btree (is_active) tablespace pg_default;

create index if not exists idx_trading_bots_created_at on public.trading_bots using btree (created_at) tablespace pg_default;

create index if not exists idx_trading_bots_risk_levels on public.trading_bots using gin (risk_levels) tablespace pg_default;

create index if not exists idx_trading_bots_ath_value on public.trading_bots using btree (all_time_high_value) tablespace pg_default;

create trigger handle_updated_at before
update on trading_bots for each row
execute function handle_updated_at ();

create trigger update_trading_bots_updated_at before
update on trading_bots for each row
execute function update_updated_at_column ();



create table
  public.trades (
    id uuid not null default gen_random_uuid (),
    bot_id uuid not null,
    coin_id text not null,
    trade_type text not null,
    quantity numeric(20, 8) not null,
    price numeric(20, 8) not null,
    total_amount numeric(20, 8) not null,
    fee numeric(20, 8) null default 0,
    indicator_data jsonb null default '{}'::jsonb,
    executed_at timestamp with time zone null default now(),
    created_at timestamp with time zone null default now(),
    updated_at timestamp with time zone null default now(),
    constraint trades_pkey primary key (id),
    constraint trades_bot_id_fkey foreign key (bot_id) references trading_bots (id) on delete cascade,
    constraint trades_trade_type_check check (
      (
        trade_type = any (array['buy'::text, 'sell'::text])
      )
    )
  ) tablespace pg_default;

create index if not exists idx_trades_bot_id on public.trades using btree (bot_id) tablespace pg_default;

create index if not exists idx_trades_coin_id on public.trades using btree (coin_id) tablespace pg_default;

create index if not exists idx_trades_executed_at on public.trades using btree (executed_at) tablespace pg_default;

create index if not exists idx_trades_trade_type on public.trades using btree (trade_type) tablespace pg_default;

create index if not exists idx_trades_bot_coin on public.trades using btree (bot_id, coin_id) tablespace pg_default;

create trigger update_trades_updated_at before
update on trades for each row
execute function update_updated_at_column ();



create table
  public.transactions (
    id uuid not null default gen_random_uuid (),
    user_id uuid not null,
    wallet_id uuid not null,
    type text not null,
    amount numeric(20, 8) not null,
    from_address text null,
    to_address text null,
    hash text null,
    status text not null,
    token text null default 'SOL'::text,
    created_at timestamp with time zone null default now(),
    updated_at timestamp with time zone null default now(),
    constraint transactions_pkey primary key (id),
    constraint transactions_hash_key unique (hash),
    constraint transactions_user_id_fkey foreign key (user_id) references auth.users (id) on delete cascade,
    constraint transactions_wallet_id_fkey foreign key (wallet_id) references wallets (id) on delete cascade,
    constraint transactions_type_check check (
      (
        type = any (
          array[
            'send'::text,
            'receive'::text,
            'swap'::text,
            'stake'::text,
            'unstake'::text
          ]
        )
      )
    ),
    constraint transactions_status_check check (
      (
        status = any (
          array[
            'pending'::text,
            'confirmed'::text,
            'failed'::text
          ]
        )
      )
    )
  ) tablespace pg_default;

create index if not exists idx_transactions_user_id on public.transactions using btree (user_id) tablespace pg_default;

create index if not exists idx_transactions_wallet_id on public.transactions using btree (wallet_id) tablespace pg_default;

create index if not exists idx_transactions_hash on public.transactions using btree (hash) tablespace pg_default;

create trigger handle_updated_at before
update on transactions for each row
execute function handle_updated_at ();