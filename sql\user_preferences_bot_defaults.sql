-- SQL script to add bot default configuration columns to user_preferences table
-- This allows users to set their preferred default values for bot creation

-- Add default strategy preference
ALTER TABLE user_preferences 
ADD COLUMN default_strategy VARCHAR(50) DEFAULT 'DCA';

-- Add default investment amount (changed from 100.0 to 1.0)
ALTER TABLE user_preferences
ADD COLUMN default_investment_amount DOUBLE PRECISION DEFAULT 1.0;

-- Add default stop loss percentage
ALTER TABLE user_preferences 
ADD COLUMN default_stop_loss DOUBLE PRECISION DEFAULT 5.0;

-- Add default take profit percentage
ALTER TABLE user_preferences 
ADD COLUMN default_take_profit DOUBLE PRECISION DEFAULT 10.0;

-- Add default risk percentage
ALTER TABLE user_preferences 
ADD COLUMN default_risk_percentage DOUBLE PRECISION DEFAULT 2.0;

-- Add default trading hours preference
ALTER TABLE user_preferences 
ADD COLUMN default_trading_24_7 BOOLEAN DEFAULT true;

-- Add default trading start time
ALTER TABLE user_preferences 
ADD COLUMN default_trading_start_time VARCHAR(10) DEFAULT '09:00';

-- Add default trading end time
ALTER TABLE user_preferences 
ADD COLUMN default_trading_end_time VARCHAR(10) DEFAULT '17:00';

-- Add default max daily trades
ALTER TABLE user_preferences 
ADD COLUMN default_max_daily_trades INTEGER DEFAULT 50;

-- Add default all time high value (in actual value, not K)
ALTER TABLE user_preferences 
ADD COLUMN default_all_time_high_value DOUBLE PRECISION DEFAULT 150000.0;

-- Add default minimum market cap
ALTER TABLE user_preferences 
ADD COLUMN default_min_market_cap DOUBLE PRECISION DEFAULT 100000.0;

-- Add default maximum market cap
ALTER TABLE user_preferences 
ADD COLUMN default_max_market_cap DOUBLE PRECISION DEFAULT 1000000.0;

-- Add default risk levels (JSON array)
ALTER TABLE user_preferences 
ADD COLUMN default_risk_levels JSONB DEFAULT '["Low", "Medium"]'::jsonb;

-- Add default start immediately preference
ALTER TABLE user_preferences 
ADD COLUMN default_start_immediately BOOLEAN DEFAULT true;

-- RSI Default Settings
ALTER TABLE user_preferences 
ADD COLUMN default_use_rsi BOOLEAN DEFAULT true;

ALTER TABLE user_preferences 
ADD COLUMN default_rsi_period INTEGER DEFAULT 14;

ALTER TABLE user_preferences 
ADD COLUMN default_rsi_overbought INTEGER DEFAULT 70;

ALTER TABLE user_preferences 
ADD COLUMN default_rsi_oversold INTEGER DEFAULT 30;

-- MACD Default Settings
ALTER TABLE user_preferences 
ADD COLUMN default_use_macd BOOLEAN DEFAULT false;

ALTER TABLE user_preferences 
ADD COLUMN default_macd_fast_period INTEGER DEFAULT 12;

ALTER TABLE user_preferences 
ADD COLUMN default_macd_slow_period INTEGER DEFAULT 26;

ALTER TABLE user_preferences 
ADD COLUMN default_macd_signal_period INTEGER DEFAULT 9;

-- Bollinger Bands Default Settings
ALTER TABLE user_preferences 
ADD COLUMN default_use_bollinger BOOLEAN DEFAULT false;

ALTER TABLE user_preferences 
ADD COLUMN default_bollinger_period INTEGER DEFAULT 20;

ALTER TABLE user_preferences 
ADD COLUMN default_bollinger_std_dev DOUBLE PRECISION DEFAULT 2.0;

-- EMA Default Settings
ALTER TABLE user_preferences 
ADD COLUMN default_use_ema BOOLEAN DEFAULT false;

ALTER TABLE user_preferences 
ADD COLUMN default_ema_fast_period INTEGER DEFAULT 12;

ALTER TABLE user_preferences 
ADD COLUMN default_ema_slow_period INTEGER DEFAULT 26;

-- KST Default Settings
ALTER TABLE user_preferences 
ADD COLUMN default_use_kst BOOLEAN DEFAULT false;

ALTER TABLE user_preferences 
ADD COLUMN default_kst_roc_periods JSONB DEFAULT '[10, 15, 20, 30]'::jsonb;

ALTER TABLE user_preferences 
ADD COLUMN default_kst_sma_periods JSONB DEFAULT '[10, 10, 10, 15]'::jsonb;

ALTER TABLE user_preferences 
ADD COLUMN default_kst_signal_period INTEGER DEFAULT 9;

-- Volume Profile Default Settings
ALTER TABLE user_preferences
ADD COLUMN default_use_volume_profile BOOLEAN DEFAULT false;

-- MACD Additional Settings
ALTER TABLE user_preferences
ADD COLUMN default_macd_fast_period INTEGER DEFAULT 12;

ALTER TABLE user_preferences
ADD COLUMN default_macd_slow_period INTEGER DEFAULT 26;

ALTER TABLE user_preferences
ADD COLUMN default_macd_signal_period INTEGER DEFAULT 9;

-- Bollinger Bands Additional Settings
ALTER TABLE user_preferences
ADD COLUMN default_bollinger_period INTEGER DEFAULT 20;

ALTER TABLE user_preferences
ADD COLUMN default_bollinger_std_dev DOUBLE PRECISION DEFAULT 2.0;

-- Add comments for documentation
COMMENT ON COLUMN user_preferences.default_strategy IS 'Default trading strategy (DCA, Scalping, etc.)';
COMMENT ON COLUMN user_preferences.default_investment_amount IS 'Default investment amount in USD';
COMMENT ON COLUMN user_preferences.default_stop_loss IS 'Default stop loss percentage';
COMMENT ON COLUMN user_preferences.default_take_profit IS 'Default take profit percentage';
COMMENT ON COLUMN user_preferences.default_risk_percentage IS 'Default risk percentage per trade';
COMMENT ON COLUMN user_preferences.default_trading_24_7 IS 'Default 24/7 trading preference';
COMMENT ON COLUMN user_preferences.default_trading_start_time IS 'Default trading start time (HH:MM format)';
COMMENT ON COLUMN user_preferences.default_trading_end_time IS 'Default trading end time (HH:MM format)';
COMMENT ON COLUMN user_preferences.default_max_daily_trades IS 'Default maximum trades per day';
COMMENT ON COLUMN user_preferences.default_all_time_high_value IS 'Default all time high value in actual amount';
COMMENT ON COLUMN user_preferences.default_min_market_cap IS 'Default minimum market cap threshold';
COMMENT ON COLUMN user_preferences.default_max_market_cap IS 'Default maximum market cap threshold';
COMMENT ON COLUMN user_preferences.default_risk_levels IS 'Default risk levels as JSON array';
COMMENT ON COLUMN user_preferences.default_start_immediately IS 'Default start immediately preference';

-- RSI Comments
COMMENT ON COLUMN user_preferences.default_use_rsi IS 'Default RSI indicator enabled state';
COMMENT ON COLUMN user_preferences.default_rsi_period IS 'Default RSI period';
COMMENT ON COLUMN user_preferences.default_rsi_overbought IS 'Default RSI overbought level';
COMMENT ON COLUMN user_preferences.default_rsi_oversold IS 'Default RSI oversold level';

-- MACD Comments
COMMENT ON COLUMN user_preferences.default_use_macd IS 'Default MACD indicator enabled state';
COMMENT ON COLUMN user_preferences.default_macd_fast_period IS 'Default MACD fast period';
COMMENT ON COLUMN user_preferences.default_macd_slow_period IS 'Default MACD slow period';
COMMENT ON COLUMN user_preferences.default_macd_signal_period IS 'Default MACD signal period';

-- Bollinger Comments
COMMENT ON COLUMN user_preferences.default_use_bollinger IS 'Default Bollinger Bands enabled state';
COMMENT ON COLUMN user_preferences.default_bollinger_period IS 'Default Bollinger Bands period';
COMMENT ON COLUMN user_preferences.default_bollinger_std_dev IS 'Default Bollinger Bands standard deviation';

-- EMA Comments
COMMENT ON COLUMN user_preferences.default_use_ema IS 'Default EMA indicator enabled state';
COMMENT ON COLUMN user_preferences.default_ema_fast_period IS 'Default EMA fast period';
COMMENT ON COLUMN user_preferences.default_ema_slow_period IS 'Default EMA slow period';

-- KST Comments
COMMENT ON COLUMN user_preferences.default_use_kst IS 'Default KST indicator enabled state';
COMMENT ON COLUMN user_preferences.default_kst_roc_periods IS 'Default KST ROC periods as JSON array';
COMMENT ON COLUMN user_preferences.default_kst_sma_periods IS 'Default KST SMA periods as JSON array';
COMMENT ON COLUMN user_preferences.default_kst_signal_period IS 'Default KST signal period';

-- Volume Profile Comments
COMMENT ON COLUMN user_preferences.default_use_volume_profile IS 'Default Volume Profile enabled state';

-- Create index for faster user preference lookups
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
