import 'package:dextrip_app/pages/trades_page.dart';
import 'package:dextrip_app/widgets/gradient_button.dart';
import 'package:dextrip_app/widgets/adaptive_navigation.dart';
import 'package:dextrip_app/utils/responsive_helper.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:universal_html/html.dart' as html;
import 'pages/coin_page.dart';
import 'pages/bot_page.dart';
import 'pages/recommendation_page.dart';
import 'pages/wallet_page.dart';
import 'pages/profile_page.dart';
import 'services/supabase_auth_service.dart';
import 'services/app_lifecycle_service.dart';
import 'package:dextrip_app/widgets/auth_bottom_sheet.dart';

import 'theme/app_theme.dart';

class ToggleHeaderIntent extends Intent {
  const ToggleHeaderIntent();
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({
    super.key,
    required this.title,
    required this.onThemeToggle,
    required this.isDarkMode,
  });
  final String title;
  final VoidCallback onThemeToggle;
  final bool isDarkMode;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  final SupabaseAuthService _authService = SupabaseAuthService();
  final AppLifecycleService _lifecycleService = AppLifecycleService();
  final ScrollController _scrollController = ScrollController();
  bool _isSignedIn = false;
  bool _isHeaderCollapsed = false;
  bool _autoCollapseEnabled = true; // Add this for auto-collapse preference
  bool _showTitle = true;
  bool _showTabBar = true; // Add this for tabbar visibility

  List<Widget> _pages = [];
  int _currentPageIndex = 0;

  // Add navigation items list
  List<NavigationItem> _navigationItems = [];

  // Page mapping
  static const List<AppPage> _pageMapping = [
    AppPage.coins,
    AppPage.bots,
    AppPage.recommendations,
    AppPage.trades,
    AppPage.wallet,
    AppPage.profile,
  ];

  // Page titles mapping
  static const List<String> _pageTitles = [
    'Coins',
    'Trading Bots',
    'Recommendations',
    'Trades',
    'Wallet',
    'Profile',
  ];

  // Web browser titles
  static const List<String> _webTitles = [
    'DexTrip - Crypto Coins & Market Data',
    'DexTrip - Trading Bots & Automation',
    'DexTrip - Trading Recommendations',
    'DexTrip - Trade History & Analytics',
    'DexTrip - Wallet & Portfolio',
    'DexTrip - User Profile & Settings',
  ];

  @override
  void initState() {
    super.initState();
    _checkAuthState();
    _setupAuthListener();
    _initializePages();
    _lifecycleService.setCurrentPage(_pageMapping[_currentPageIndex]);
    _updateWebTitle();
    _setupScrollListener(); // Add scroll listener
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Initialize navigation items here where MediaQuery is available
    _initializeNavigationItems();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (!_autoCollapseEnabled) return;
      
      const threshold = 100.0;
      final shouldCollapse = _scrollController.offset > threshold;
      
      if (shouldCollapse != _isHeaderCollapsed) {
        setState(() {
          _isHeaderCollapsed = shouldCollapse;
        });
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _initializeNavigationItems() {
    // On desktop, show only coins page when not signed in
    if (ResponsiveHelper.isDesktop(context) && !_isSignedIn) {
      _navigationItems = [
        NavigationItem(icon: LucideIcons.circleDot, label: 'Coins'),
      ];
    } else {
      // Show all navigation items for mobile or when signed in
      _navigationItems = [
        NavigationItem(icon: LucideIcons.circleDot, label: 'Coins'),
        NavigationItem(icon: LucideIcons.bot, label: 'Bots'),
        NavigationItem(icon: LucideIcons.lightbulb, label: 'Recommendations'),
        NavigationItem(icon: LucideIcons.trendingUp, label: 'Trades'),
        NavigationItem(icon: LucideIcons.wallet, label: 'Wallet'),
        NavigationItem(icon: LucideIcons.user, label: 'Profile'),
      ];
    }
  }

  void _updateWebTitle() {
    if (kIsWeb) {
      html.document.title = _webTitles[_currentPageIndex];
    }
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentPageIndex = index;
    });
    _lifecycleService.setCurrentPage(_pageMapping[index]);
    _updateWebTitle(); // Update web title when page changes
  }

  void _initializePages() {
    debugPrint('🔄 Initializing pages with auth state: $_isSignedIn');
    _pages = [
      CoinPage(
        onThemeToggle: widget.onThemeToggle,
        isDarkMode: widget.isDarkMode,
        isSignedIn: _isSignedIn,
        onSignIn: _handleSignIn,
      ),
      const BotPage(),
      const RecommendationPage(),
      const TradesPage(),
      const WalletPage(),
      const ProfilePage(),
    ];
    debugPrint('✅ Pages initialized: ${_pages.length} pages');
  }

  void _checkAuthState() {
    setState(() {
      _isSignedIn = _authService.isAuthenticated;
    });

    // Debug user info if authenticated
    if (_isSignedIn) {
      final user = _authService.currentUser;
      debugPrint('✅ User authenticated:');
      debugPrint('  ID: ${user?.id}');
      debugPrint('  Email: ${user?.email}');
      debugPrint('  Name: ${user?.userMetadata?['full_name'] ?? user?.userMetadata?['name']}');
    }
  }

  void _handleSignIn() {
    debugPrint('🔄 Handling sign in...');

    setState(() {
      _isSignedIn = true;
      _currentPageIndex = 0; // Immediately set to Coins page
    });

    _initializeNavigationItems(); // Reinitialize navigation items
    _initializePages(); // Reinitialize pages with new auth state

    // Multiple rebuild attempts to ensure proper state update
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _currentPageIndex = 0; // Ensure we're on Coins page
        });

        // Additional rebuild after a short delay
        Future.delayed(const Duration(milliseconds: 200), () {
          if (mounted) {
            setState(() {});
            debugPrint('✅ Sign in handling complete - Current page: $_currentPageIndex');
          }
        });
      }
    });
  }

  void _setupAuthListener() {
    try {
      // Listen to Supabase auth state changes
      Supabase.instance.client.auth.onAuthStateChange.listen((data) {
        final AuthChangeEvent event = data.event;
        if (event == AuthChangeEvent.signedIn) {
          debugPrint('🔄 Auth listener: User signed in');
          setState(() {
            _isSignedIn = true;
            _currentPageIndex = 0; // Immediately set to Coins page
          });
          _initializeNavigationItems(); // Reinitialize navigation items
          _initializePages(); // Reinitialize pages with new auth state

          // Multiple rebuild attempts to ensure proper state update
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _currentPageIndex = 0; // Ensure we're on the Coins page
              });

              // Additional rebuild after a short delay
              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted) {
                  setState(() {});
                  debugPrint('✅ Auth listener: Page rebuild complete');
                }
              });
            }
          });
        } else if (event == AuthChangeEvent.signedOut) {
          setState(() {
            _isSignedIn = false;
            _currentPageIndex = 0; // Navigate back to Coins page
          });
          _initializeNavigationItems(); // Reinitialize navigation items
          _initializePages(); // Reinitialize pages with new auth state

          // Show success message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('✅ Signed out successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      });
      debugPrint('✅ Supabase auth listener set up');
    } catch (e) {
      debugPrint('Auth listener setup failed: $e');
    }
  }

  void _showProfileDialog() {
    final user = _authService.currentUser;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Profile Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProfileRow('User ID', user?.id ?? 'N/A'),
            _buildProfileRow('Email', user?.email ?? 'N/A'),
            _buildProfileRow('Name', user?.userMetadata?['full_name'] ?? 'N/A'),
            _buildProfileRow('Auth Method', 'Supabase OTP'),
            if (user?.createdAt != null)
              _buildProfileRow('Created', user!.createdAt),
            if (user?.lastSignInAt != null)
              _buildProfileRow('Last Sign In', user!.lastSignInAt!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleLogout() async {
    try {
      await _authService.signOut();
      setState(() {
        _isSignedIn = false;
      });
      _initializePages(); // Reinitialize pages

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('✅ Signed out successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ Error signing out: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('🏗️ Building homepage - Signed in: $_isSignedIn, Current page: $_currentPageIndex');
    if (ResponsiveHelper.isDesktop(context)) {
      return Shortcuts(
        shortcuts: <LogicalKeySet, Intent>{
          LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyH): 
              const ToggleHeaderIntent(),
        },
        child: Actions(
          actions: <Type, Action<Intent>>{
            ToggleHeaderIntent: CallbackAction<ToggleHeaderIntent>(
              onInvoke: (ToggleHeaderIntent intent) {
                setState(() {
                  _isHeaderCollapsed = !_isHeaderCollapsed;
                });
                return null;
              },
            ),
          },
          child: Focus(
            autofocus: true,
            child: _buildDesktopLayout(),
          ),
        ),
      );
    } else {
      return _buildMobileLayout();
    }
  }

  Widget _buildDesktopLayout() {
    return Scaffold(
      body: Column(
        children: [
          // Desktop Header with TabBar and Coin Count
          if (_isSignedIn) _buildDesktopHeaderWithTabs(),
          

          // Main content area
          Expanded(
            child: Row(
              children: [
                // Side navigation (logo + toggle + menu items)
                _buildDesktopSidePanel(),

                // Main content
                Expanded(
                  child: _pages.isNotEmpty && _currentPageIndex < _pages.length
                    ? _pages[_currentPageIndex]
                    : Container(
                        child: Center(
                          child: CircularProgressIndicator(),
                        ),
                      ),
                ),
              ],
            ),
          ),

          // Bottom bar (simplified)
          if (_isSignedIn) _buildDesktopBottomBar(),
        ],
      ),
    );
  }

  Widget _buildDesktopSidePanel() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOutCubic,
      width: _showTabBar ? 280 : 80,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).dividerColor.withValues(alpha: 0.1),
          ),
        ),
      ),
      child: Column(
        children: [
          const SizedBox(height: 32),
          
          // Logo with toggle button
          Padding(
            padding: EdgeInsets.symmetric(horizontal: _showTabBar ? 24 : 12),
            child: _showTabBar 
                ? Row(
                    children: [
                      // Logo
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          gradient: AppTheme.primaryGradient,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          LucideIcons.zap,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      
                      const SizedBox(width: 12),
                      
                      Expanded(
                        child: Text(
                          'DexTrip',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      
                      // Toggle button
                      IconButton(
                        onPressed: () {
                          setState(() {
                            _showTabBar = !_showTabBar;
                          });
                        },
                        icon: const Icon(
                          LucideIcons.panelLeftClose,
                          size: 20,
                        ),
                        tooltip: 'Collapse sidebar',
                        style: IconButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.surface.withValues(alpha: 0.5),
                          foregroundColor: Theme.of(context).textTheme.bodyMedium?.color,
                          minimumSize: const Size(32, 32),
                          padding: const EdgeInsets.all(6),
                        ),
                      ),
                    ],
                  )
                : Column(
                    children: [
                      // Collapsed logo
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          gradient: AppTheme.primaryGradient,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          LucideIcons.zap,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // Expand button (centered)
                      IconButton(
                        onPressed: () {
                          setState(() {
                            _showTabBar = !_showTabBar;
                          });
                        },
                        icon: const Icon(
                          LucideIcons.panelLeftOpen,
                          size: 18,
                        ),
                        tooltip: 'Expand sidebar',
                        style: IconButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.surface.withValues(alpha: 0.5),
                          foregroundColor: Theme.of(context).textTheme.bodyMedium?.color,
                          minimumSize: const Size(32, 32),
                          padding: const EdgeInsets.all(6),
                        ),
                      ),
                    ],
                  ),
          ),
          
          const SizedBox(height: 32),
          
          // Navigation items (expanded)
          if (_showTabBar)
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _navigationItems.length,
                itemBuilder: (context, index) {
                  final item = _navigationItems[index];
                  final isSelected = index == _currentPageIndex;
                  
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () => _onPageChanged(index),
                        borderRadius: BorderRadius.circular(12),
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          decoration: BoxDecoration(
                            gradient: isSelected ? AppTheme.primaryGradient : null,
                            color: isSelected ? null : Colors.transparent,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: isSelected ? [
                              BoxShadow(
                                color: AppTheme.primaryGradient.colors.first.withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ] : null,
                          ),
                          child: Row(
                            children: [
                              Icon(
                                item.icon,
                                size: 20,
                                color: isSelected
                                    ? Colors.white
                                    : Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  item.label,
                                  style: TextStyle(
                                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                                    color: isSelected
                                        ? Colors.white
                                        : Theme.of(context).textTheme.bodyMedium?.color,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          
          // Navigation items (collapsed - only icons)
          if (!_showTabBar)
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                itemCount: _navigationItems.length,
                itemBuilder: (context, index) {
                  final item = _navigationItems[index];
                  final isSelected = index == _currentPageIndex;
                  
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Tooltip(
                      message: item.label,
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () => _onPageChanged(index),
                          borderRadius: BorderRadius.circular(12),
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            width: 56,
                            height: 56,
                            decoration: BoxDecoration(
                              gradient: isSelected ? AppTheme.primaryGradient : null,
                              color: isSelected ? null : Colors.transparent,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: isSelected ? [
                                BoxShadow(
                                  color: AppTheme.primaryGradient.colors.first.withValues(alpha: 0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ] : null,
                            ),
                            child: Icon(
                              item.icon,
                              size: 20,
                              color: isSelected
                                  ? Colors.white
                                  : Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),

          // Menu items section
          if (_showTabBar) _buildSidebarMenuItems(),
          if (!_showTabBar) _buildCollapsedMenuItems(),
        ],
      ),
    );
  }

  Widget _buildSidebarMenuItems() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          const Divider(),
          const SizedBox(height: 16),

          // Login/Get Started button
          if (!_isSignedIn)
            SizedBox(
              width: double.infinity,
              child: GradientButton(
                text: 'Get Started',
                onPressed: _showAuthBottomSheet,
                icon: LucideIcons.arrowRight,
              ),
            ),


        ],
      ),
    );
  }

  Widget _buildCollapsedMenuItems() {
    return Container(
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          const Divider(),
          const SizedBox(height: 8),

          // Login/Get Started button (icon only)
          if (!_isSignedIn)
            Tooltip(
              message: 'Get Started',
              child: IconButton(
                onPressed: _showAuthBottomSheet,
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    LucideIcons.arrowRight,
                    size: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ),

          if (_isSignedIn) ...[
            // User icon
            Tooltip(
              message: 'Signed In',
              child: CircleAvatar(
                radius: 16,
                backgroundColor: AppTheme.primaryColor,
                child: const Icon(
                  LucideIcons.user,
                  size: 16,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
          ],

          // Theme toggle (compact)
          _buildCompactThemeToggle(),
        ],
      ),
    );
  }

  Widget _buildSidebarThemeToggle() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            LucideIcons.palette,
            size: 16,
            color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Theme',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          _buildAnimatedThemeSwitch(),
        ],
      ),
    );
  }

  Widget _buildCompactThemeToggle() {
    return Tooltip(
      message: widget.isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode',
      child: GestureDetector(
        onTap: widget.onThemeToggle,
        child: Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: Icon(
                widget.isDarkMode ? LucideIcons.moon : LucideIcons.sun,
                key: ValueKey(widget.isDarkMode),
                size: 16,
                color: widget.isDarkMode
                    ? Colors.blue[300]
                    : Colors.orange[600],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDesktopHeaderWithTabs() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      height: _showTitle ? (_isHeaderCollapsed ? 60 : 70) : 0,
      child: _showTitle
          ? Container(
              padding: EdgeInsets.symmetric(
                horizontal: 32,
                vertical: _isHeaderCollapsed ? 8 : 10,
              ),
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                border: Border(
                  bottom: BorderSide(
                    color: Theme.of(context).dividerColor.withValues(alpha: 0.1),
                  ),
                ),
                boxShadow: _isHeaderCollapsed
                    ? [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
              child: Row(
                children: [
                  // Header controls
                  PopupMenuButton<String>(
                    icon: AnimatedRotation(
                      turns: _isHeaderCollapsed ? 0.5 : 0,
                      duration: const Duration(milliseconds: 300),
                      child: Icon(
                        LucideIcons.chevronUp,
                        size: 20,
                        color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                      ),
                    ),
                    tooltip: 'Header options',
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'toggle_header',
                        child: Row(
                          children: [
                            Icon(
                              _isHeaderCollapsed ? LucideIcons.chevronDown : LucideIcons.chevronUp,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(_isHeaderCollapsed ? 'Expand header' : 'Collapse header'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'auto_collapse',
                        child: Row(
                          children: [
                            Icon(
                              _autoCollapseEnabled ? LucideIcons.check : LucideIcons.square,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            const Text('Auto-collapse on scroll'),
                          ],
                        ),
                      ),
                    ],
                    onSelected: (value) {
                      switch (value) {
                        case 'toggle_header':
                          setState(() {
                            _isHeaderCollapsed = !_isHeaderCollapsed;
                          });
                          break;
                        case 'auto_collapse':
                          setState(() {
                            _autoCollapseEnabled = !_autoCollapseEnabled;
                          });
                          break;
                      }
                    },
                  ),

                  const SizedBox(width: 16),

                  // Page title with animation
                  AnimatedDefaultTextStyle(
                    duration: const Duration(milliseconds: 300),
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: _isHeaderCollapsed ? 18 : 24,
                        ) ??
                        const TextStyle(),
                    child: Text(_pageTitles[_currentPageIndex]),
                  ),

                  // Page subtitle (only when expanded)
                  if (!_isHeaderCollapsed) ...[
                    const SizedBox(width: 16),
                    AnimatedOpacity(
                      duration: const Duration(milliseconds: 300),
                      opacity: _isHeaderCollapsed ? 0 : 1,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _getPageSubtitle(),
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],

                  const Spacer(),

                  // Quick actions (when collapsed)
                  if (_isHeaderCollapsed) ...[
                    _buildQuickActions(),
                    const SizedBox(width: 16),
                  ],

                  // User profile
                  _buildUserProfile(),
                ],
              ),
            )
          : const SizedBox.shrink(),
    );
  }

  Widget _buildDesktopBottomBar() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor.withValues(alpha: 0.1),
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Title toggle button
          Container(
            decoration: BoxDecoration(
              color: _showTitle
                  ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                  : Theme.of(context).colorScheme.surface.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _showTitle
                    ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
                    : Theme.of(context).dividerColor.withValues(alpha: 0.2),
              ),
            ),
            child: IconButton(
              onPressed: () {
                setState(() {
                  _showTitle = !_showTitle;
                });
              },
              icon: AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                child: Icon(
                  _showTitle ? LucideIcons.eyeOff : LucideIcons.eye,
                  key: ValueKey(_showTitle),
                  size: 18,
                  color: _showTitle
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                ),
              ),
              tooltip: _showTitle ? 'Hide title bar' : 'Show title bar',
            ),
          ),

          const SizedBox(width: 16),

          // Status indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.green.withValues(alpha: 0.3),
                width: 0.5,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Connected',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.green[400],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          const Spacer(),

        
          const SizedBox(width: 24),

          // Animated Dark Mode Switch
          _buildAnimatedThemeSwitch(),
        ],
      ),
    );
  }

  Widget _buildAnimatedThemeSwitch() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: Theme.of(context).dividerColor.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Light mode indicator
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: !widget.isDarkMode
                  ? Theme.of(context).colorScheme.primary
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              LucideIcons.sun,
              size: 16,
              color: !widget.isDarkMode
                  ? Colors.white
                  : Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.5),
            ),
          ),

          const SizedBox(width: 4),

          // Toggle switch
          GestureDetector(
            onTap: widget.onThemeToggle,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              width: 50,
              height: 24,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: widget.isDarkMode
                    ? Theme.of(context).colorScheme.primary
                    : Colors.grey[300],
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  AnimatedPositioned(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    left: widget.isDarkMode ? 26 : 2,
                    top: 2,
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 200),
                        child: Icon(
                          widget.isDarkMode ? LucideIcons.moon : LucideIcons.sun,
                          key: ValueKey(widget.isDarkMode),
                          size: 12,
                          color: widget.isDarkMode
                              ? Theme.of(context).colorScheme.primary
                              : Colors.orange[600],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(width: 4),

          // Dark mode indicator
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: widget.isDarkMode
                  ? Theme.of(context).colorScheme.primary
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              LucideIcons.moon,
              size: 16,
              color: widget.isDarkMode
                  ? Colors.white
                  : Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }



  String _getPageSubtitle() {
    switch (_currentPageIndex) {
      case 0:
        return 'Real-time market data';
      case 1:
        return 'Automated trading';
      case 2:
        return 'AI-powered insights';
      case 3:
        return 'Trading history';
      case 4:
        return 'Portfolio management';
      case 5:
        return 'Account settings';
      default:
        return '';
    }
  }

  Widget _buildUserProfile() {
    final user = _authService.currentUser;
    final userName = user?.userMetadata?['full_name'] ??
                    user?.userMetadata?['name'] ??
                    user?.email?.split('@')[0] ?? 'User';
    final userEmail = user?.email ?? '';
    final userPicture = user?.userMetadata?['avatar_url'] as String?;

    return PopupMenuButton<String>(
      tooltip: 'User menu',
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: EdgeInsets.symmetric(
          horizontal: _isHeaderCollapsed ? 8 : 12,
          vertical: _isHeaderCollapsed ? 6 : 5,
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(_isHeaderCollapsed ? 16 : 20),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircleAvatar(
              radius: _isHeaderCollapsed ? 12 : 16,
              backgroundColor: AppTheme.primaryColor,
              backgroundImage: userPicture != null ? NetworkImage(userPicture) : null,
              child: userPicture == null ? Icon(
                LucideIcons.user,
                size: _isHeaderCollapsed ? 12 : 16,
                color: Colors.white,
              ) : null,
            ),
            if (!_isHeaderCollapsed) ...[
              const SizedBox(width: 8),
              AnimatedOpacity(
                duration: const Duration(milliseconds: 300),
                opacity: _isHeaderCollapsed ? 0 : 1,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      userName,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                    if (userEmail.isNotEmpty)
                      Text(
                        userEmail,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                  ],
                ),
              ),
              const SizedBox(width: 4),
              Icon(
                LucideIcons.chevronDown,
                size: 14,
                color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.5),
              ),
            ],
          ],
        ),
      ),
      itemBuilder: (context) => [
        PopupMenuItem(
          enabled: false,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                userName,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
              if (userEmail.isNotEmpty)
                Text(
                  userEmail,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              const Divider(),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'profile',
          child: Row(
            children: [
              Icon(LucideIcons.user, size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Profile Settings',
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'logout',
          child: Row(
            children: [
              Icon(LucideIcons.logOut, size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Sign Out',
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ],
      onSelected: (value) async {
        switch (value) {
          case 'profile':
            _showProfileDialog();
            break;
          case 'logout':
            await _handleLogout();
            break;
        }
      },
    );
  }

  Widget _buildQuickActions() {
    switch (_currentPageIndex) {
      case 0: // Coins
        return Row(
          children: [
            IconButton(
              onPressed: () {
                // Focus search in coin page
              },
              icon: const Icon(LucideIcons.search, size: 18),
              tooltip: 'Search coins',
            ),
            IconButton(
              onPressed: () {
                // Show filter modal
              },
              icon: const Icon(LucideIcons.file, size: 18),
              tooltip: 'Filter coins',
            ),
          ],
        );
     
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildMobileLayout() {
    return Scaffold(
      body: _pages[_currentPageIndex],
      bottomNavigationBar: !_isSignedIn
          ? _buildGetStartedFooter()
          : _buildBottomNavigation(),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
          child: GNav(
            rippleColor: Colors.grey[300]!,
            hoverColor: Colors.grey[100]!,
            gap: 8,
            activeColor: Colors.white,
            iconSize: 24,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            duration: const Duration(milliseconds: 400),
            tabBackgroundGradient: AppTheme.primaryGradient,
            color: Colors.grey[600],
            tabs: _navigationItems.map((item) => GButton(
              icon: item.icon,
             
            )).toList(),
            selectedIndex: _currentPageIndex,
            onTabChange: _onPageChanged,
          ),
        ),
      ),
    );
  }

  Widget _buildGetStartedFooter() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
        child: GradientButton(
          text: 'Get Started',
          onPressed: _showAuthBottomSheet,
          // icon: LucideIcons.arrowRight,
        ),
      ),
    );
  }

  void _showAuthBottomSheet() {
    // Use responsive auth modal
    if (ResponsiveHelper.isDesktop(context)) {
      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            constraints: BoxConstraints(
              maxWidth: 500,
              maxHeight: MediaQuery.of(context).size.height * 0.9,
            ),
            child: AuthBottomSheet(
              onSuccess: () {
                // Don't call Navigator.pop here since AuthBottomSheet already does it
                _handleSignIn();
              },
            ),
          ),
        ),
      );
    } else {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => AuthBottomSheet(
          onSuccess: () {
            _handleSignIn();
          },
        ),
      );
    }
  }
}
